import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Animated, Dimensions, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Menu, X, Settings, User, Bell, HelpCircle, Shield, LogOut, BookOpen } from 'lucide-react-native';
import { router } from 'expo-router';
import { colors } from '../utils/colors';

const { width: screenWidth } = Dimensions.get('window');

interface HamburgerMenuProps {
  position?: 'top-right' | 'top-left';
}

export default function HamburgerMenu({ position = 'top-right' }: HamburgerMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [slideAnimation] = useState(new Animated.Value(-screenWidth));

  const toggleMenu = () => {
    if (isOpen) {
      // Close menu
      Animated.timing(slideAnimation, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }).start(() => setIsOpen(false));
    } else {
      // Open menu
      setIsOpen(true);
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  };

  const closeMenu = () => {
    Animated.timing(slideAnimation, {
      toValue: -screenWidth,
      duration: 300,
      useNativeDriver: true,
    }).start(() => setIsOpen(false));
  };

  const handleLogout = () => {
    // Implement proper logout functionality
    Alert.alert(
      'Logout Confirmation',
      'Are you sure you want to log out? This will end your current session.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive', 
          onPress: async () => {
            try {
              closeMenu();
              
              // TODO: Implement actual logout logic
              // - Clear user tokens from secure storage
              // - Clear local user data
              // - Reset app state
              // - Navigate to login screen
              
              // For now, we'll simulate the logout process
              Alert.alert(
                'Logging Out...',
                'Clearing your session and returning to login...',
                [{ 
                  text: 'OK', 
                  onPress: () => {
                    // TODO: Replace with actual logout navigation
                    // router.replace('/login');
                    console.log('User logged out successfully');
                    
                    // Show success message
                    Alert.alert(
                      'Logged Out Successfully',
                      'You have been logged out. Thank you for using Everlasting Us! 💖',
                      [{ text: 'Goodbye 👋' }]
                    );
                  }
                }]
              );
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert(
                'Logout Error',
                'There was an issue logging you out. Please try again.',
                [{ text: 'OK' }]
              );
            }
          }
        }
      ]
    );
  };

  const handleMenuItemPress = (route: string) => {
    closeMenu();
    router.push(route);
  };

  const menuItems = [
    {
      icon: Settings,
      title: 'Settings',
      route: '/app-settings',
      color: colors.primary,
    },
    {
      icon: BookOpen,
      title: 'Scrapbook',
      route: '/scrapbook',
      color: colors.purple,
    },
    {
      icon: User,
      title: 'Profile',
      route: '/couple-profile',
      color: colors.secondary,
    },
    {
      icon: Bell,
      title: 'Notifications',
      route: '/notifications',
      color: colors.warning,
    },
    {
      icon: HelpCircle,
      title: 'Help & Support',
      route: '/help',
      color: colors.blue,
    },
    {
      icon: Shield,
      title: 'Privacy & Security',
      route: '/security',
      color: colors.success,
    },
  ];

  return (
    <>
      {/* Hamburger Button */}
      <TouchableOpacity
        style={[
          styles.hamburgerButton,
          position === 'top-right' ? styles.topRight : styles.topLeft
        ]}
        onPress={toggleMenu}
      >
        <Menu size={24} color={colors.textInverse} />
      </TouchableOpacity>

      {/* Menu Modal */}
      <Modal
        visible={isOpen}
        transparent
        animationType="none"
        onRequestClose={closeMenu}
      >
        <View style={styles.modalOverlay}>
          {/* Backdrop */}
          <TouchableOpacity
            style={styles.backdrop}
            activeOpacity={1}
            onPress={closeMenu}
          />
          
          {/* Menu Content */}
          <Animated.View
            style={[
              styles.menuContainer,
              {
                transform: [{ translateX: slideAnimation }],
                right: position === 'top-right' ? 0 : undefined,
                left: position === 'top-left' ? 0 : undefined,
              }
            ]}
          >
            {/* Menu Header */}
            <LinearGradient
              colors={colors.gradients.primary}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.menuHeader}
            >
              <Text style={styles.menuTitle}>Menu</Text>
              <TouchableOpacity onPress={closeMenu} style={styles.closeButton}>
                <X size={24} color={colors.textInverse} />
              </TouchableOpacity>
            </LinearGradient>

            {/* Menu Items */}
            <View style={styles.menuItems}>
              {menuItems.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.menuItem}
                  onPress={() => handleMenuItemPress(item.route)}
                >
                  <View style={[styles.menuItemIcon, { backgroundColor: item.color }]}>
                    <item.icon size={20} color={colors.textInverse} />
                  </View>
                  <Text style={styles.menuItemText}>{item.title}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Logout Section */}
            <View style={styles.logoutSection}>
              <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                <LogOut size={20} color={colors.error} />
                <Text style={styles.logoutText}>Log Out</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  hamburgerButton: {
    position: 'absolute',
    top: 60,
    zIndex: 1000,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  topRight: {
    right: 20,
  },
  topLeft: {
    left: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdrop: {
    flex: 1,
  },
  menuContainer: {
    position: 'absolute',
    top: 0,
    width: screenWidth * 0.8,
    height: '100%',
    backgroundColor: colors.backgroundSecondary,
    shadowColor: colors.shadow,
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
  },
  menuHeader: {
    padding: 20,
    paddingTop: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  menuTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textInverse,
  },
  closeButton: {
    padding: 8,
  },
  menuItems: {
    padding: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: colors.backgroundPrimary,
  },
  menuItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  logoutSection: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
    marginTop: 'auto',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    backgroundColor: colors.backgroundPrimary,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.error,
    marginLeft: 8,
  },
});
