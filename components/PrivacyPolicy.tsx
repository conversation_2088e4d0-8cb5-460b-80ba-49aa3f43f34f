import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Shield, Lock, Eye, Database, Users, ArrowLeft } from 'lucide-react-native';
import { colors } from '../utils/colors';

interface PrivacyPolicyProps {
  onBack: () => void;
}

export default function PrivacyPolicy({ onBack }: PrivacyPolicyProps) {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[colors.lightPink, colors.lightPurple]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Shield size={32} color={colors.white} />
        <Text style={styles.headerTitle}>Privacy Policy</Text>
        <Text style={styles.headerSubtitle}>Your data security is our priority</Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Lock size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Data Collection</Text>
          </View>
          <Text style={styles.sectionText}>
            We collect only the information you voluntarily provide:{'\n\n'}
            • Account Information: Email address (for registered users only){'\n'}
            • Profile Data: Partner names and selected avatars{'\n'}
            • Activity Responses: Your answers to relationship exercises{'\n'}
            • Photos: Images you choose to add to your shared memories{'\n'}
            • Usage Data: Basic app functionality data (no personal tracking){'\n\n'}
            We do not collect location data, contacts, or any information without your explicit consent.
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Database size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Data Storage</Text>
          </View>
          <Text style={styles.sectionText}>
            Your data is stored using a hybrid approach for optimal security and functionality:{'\n\n'}
            • Local Storage: All data is encrypted and stored locally on your device using secure storage{'\n'}
            • Cloud Backup: When you create an account, your data is securely backed up to Supabase (a trusted, GDPR-compliant cloud service) for synchronization across devices{'\n'}
            • Guest Mode: If you use the app without an account, all data remains local only{'\n\n'}
            We use industry-standard encryption for all data transmission and storage.
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Eye size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Data Usage</Text>
          </View>
          <Text style={styles.sectionText}>
            Your data is used solely to provide the relationship-building features within the app. 
            We do not analyze, sell, or share your personal information with advertisers or other services.
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Users size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Data Sharing & Third Parties</Text>
          </View>
          <Text style={styles.sectionText}>
            We do not sell, rent, or share your personal data with advertisers or marketing companies.{'\n\n'}
            Third-party services we use:{'\n'}
            • Supabase: Secure cloud database for authenticated users (GDPR compliant, encrypted){'\n'}
            • No analytics or tracking services{'\n'}
            • No advertising networks{'\n\n'}
            Your relationship information remains private and is only accessible to you through your account.
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Database size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Data Transmission</Text>
          </View>
          <Text style={styles.sectionText}>
            When you create an account, the following data is transmitted securely to our cloud service:{'\n\n'}
            • Account information (email, encrypted password){'\n'}
            • Relationship activities and responses you choose to save{'\n'}
            • Photos you add to your shared memories{'\n\n'}
            All data transmission uses HTTPS encryption and industry-standard security protocols.
            Guest users' data never leaves their device.
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Shield size={20} color={colors.lightPink} />
            <Text style={styles.sectionTitle}>Security Measures</Text>
          </View>
          <Text style={styles.sectionText}>
            We implement comprehensive security practices:{'\n\n'}
            • End-to-end encryption for data transmission{'\n'}
            • Secure local storage with device-level encryption{'\n'}
            • Regular security audits and updates{'\n'}
            • No data stored in plain text{'\n'}
            • Secure authentication and session management
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Rights</Text>
          <Text style={styles.sectionText}>
            • Access and review your stored data{'\n'}
            • Delete your data at any time{'\n'}
            • Export your data for backup{'\n'}
            • Opt out of data collection
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          <Text style={styles.sectionText}>
            If you have questions about this privacy policy or our data practices, 
            please contact us through the app settings.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Updates to Policy</Text>
          <Text style={styles.sectionText}>
            This privacy policy may be updated periodically. We will notify you of any 
            significant changes through the app.
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Last updated: {new Date().toLocaleDateString()}
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    padding: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: 8,
  },
  sectionText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
});
