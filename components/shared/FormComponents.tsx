/**
 * Form Components
 * 
 * Shared form components to reduce duplication across week screens.
 * Provides consistent styling and behavior for common form elements.
 * 
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { colors } from '../../utils/colors';

/**
 * Props for the FormInput component
 */
export interface FormInputProps {
  /** Label text */
  label: string;
  /** Input value */
  value: string;
  /** Function to handle value changes */
  onChangeText: (text: string) => void;
  /** Placeholder text */
  placeholder?: string;
  /** Whether the input is multiline */
  multiline?: boolean;
  /** Number of lines for multiline input */
  numberOfLines?: number;
  /** Whether the input is required */
  required?: boolean;
  /** Custom style */
  style?: any;
  /** Whether the input is disabled */
  disabled?: boolean;
}

/**
 * FormInput Component
 * 
 * Standardized input component with consistent styling and behavior.
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const FormInput: React.FC<FormInputProps> = ({
  label,
  value,
  onChangeText,
  placeholder,
  multiline = false,
  numberOfLines = 1,
  required = false,
  style,
  disabled = false,
}) => (
  <View style={[styles.inputGroup, style]}>
    <Text style={styles.inputLabel}>
      {label}
      {required && <Text style={styles.required}> *</Text>}
    </Text>
    <TextInput
      style={[
        styles.textInput,
        multiline && styles.multilineInput,
        disabled && styles.disabledInput,
      ]}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={colors.textSecondary}
      multiline={multiline}
      numberOfLines={numberOfLines}
      editable={!disabled}
    />
  </View>
);

/**
 * Props for the FormButton component
 */
export interface FormButtonProps {
  /** Button text */
  title: string;
  /** Function to handle button press */
  onPress: () => void;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Custom style */
  style?: any;
  /** Icon to display */
  icon?: React.ReactNode;
}

/**
 * FormButton Component
 * 
 * Standardized button component with consistent styling and variants.
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const FormButton: React.FC<FormButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  style,
  icon,
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    switch (variant) {
      case 'secondary':
        return [...baseStyle, styles.secondaryButton];
      case 'success':
        return [...baseStyle, styles.successButton];
      case 'danger':
        return [...baseStyle, styles.dangerButton];
      default:
        return [...baseStyle, styles.primaryButton];
    }
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    switch (variant) {
      case 'secondary':
        return [...baseStyle, styles.secondaryButtonText];
      case 'success':
        return [...baseStyle, styles.successButtonText];
      case 'danger':
        return [...baseStyle, styles.dangerButtonText];
      default:
        return [...baseStyle, styles.primaryButtonText];
    }
  };

  return (
    <TouchableOpacity
      style={[
        ...getButtonStyle(),
        disabled && styles.disabledButton,
        style,
      ]}
      onPress={onPress}
      disabled={disabled}
    >
      {icon && <View style={styles.buttonIcon}>{icon}</View>}
      <Text style={[
        ...getTextStyle(),
        disabled && styles.disabledButtonText,
      ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

/**
 * Props for the FormSection component
 */
export interface FormSectionProps {
  /** Section title */
  title: string;
  /** Section subtitle */
  subtitle?: string;
  /** Children content */
  children: React.ReactNode;
  /** Custom style */
  style?: any;
}

/**
 * FormSection Component
 * 
 * Container component for grouping related form elements.
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const FormSection: React.FC<FormSectionProps> = ({
  title,
  subtitle,
  children,
  style,
}) => (
  <View style={[styles.sectionContainer, style]}>
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {subtitle && <Text style={styles.sectionSubtitle}>{subtitle}</Text>}
    </View>
    {children}
  </View>
);

/**
 * Props for the FormCard component
 */
export interface FormCardProps {
  /** Children content */
  children: React.ReactNode;
  /** Custom style */
  style?: any;
}

/**
 * FormCard Component
 * 
 * Card container for form content with consistent styling.
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const FormCard: React.FC<FormCardProps> = ({ children, style }) => (
  <View style={[styles.card, style]}>
    {children}
  </View>
);

const styles = StyleSheet.create({
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  required: {
    color: colors.error,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: colors.textPrimary,
    backgroundColor: colors.background,
    minHeight: 48,
  },
  multilineInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  disabledInput: {
    backgroundColor: colors.backgroundSecondary,
    color: colors.textSecondary,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    minHeight: 48,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  secondaryButton: {
    backgroundColor: colors.backgroundSecondary,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  successButton: {
    backgroundColor: colors.success,
  },
  dangerButton: {
    backgroundColor: colors.error,
  },
  disabledButton: {
    backgroundColor: colors.backgroundSecondary,
    opacity: 0.6,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  primaryButtonText: {
    color: colors.white,
  },
  secondaryButtonText: {
    color: colors.textPrimary,
  },
  successButtonText: {
    color: colors.white,
  },
  dangerButtonText: {
    color: colors.white,
  },
  disabledButtonText: {
    color: colors.textSecondary,
  },
  buttonIcon: {
    marginRight: 8,
  },
  sectionContainer: {
    marginBottom: 32,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});
