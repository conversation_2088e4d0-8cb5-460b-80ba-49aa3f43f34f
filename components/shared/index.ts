/**
 * Shared Components Index
 * 
 * Centralized export for all shared UI components.
 * This provides a single import point for all reusable components.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

// Enhanced Components
export {
  GradientCard,
  StatCard,
  GradientButton,
  ProgressBar,
  WeekSelector,
  EnhancedModal,
  EnhancedInput,
  ActivityCard,
  StepNavigation,
} from './EnhancedComponents';

// Layout Components
export {
  ScreenLayout,
  HeaderLayout,
  ContentLayout,
  CardGrid,
  SectionLayout,
  FlexRow,
  FlexColumn,
  Spacer,
  Divider,
} from './LayoutComponents';

// Story Components
export {
  StoryHeader,
  StoryTimelineItem,
  StorySectionEditor,
  StoryProgressBar,
  StoryNavigation,
  StoryFooter,
  StoryIntroCard,
  StoryFooterCard,
} from './StoryComponents';

// Authentication Components
export {
  AuthScreenLayout,
  AuthHeader,
  AuthInput,
  AuthButton,
  FeatureCard,
  FeatureShowcase,
  OnboardingStep,
  OnboardingNavigation,
} from './AuthComponents';

// Legacy Common Components (for backward compatibility)
export {
  Card,
  GradientButton as LegacyGradientButton,
  StyledInput,
  Avatar,
  SectionHeader,
  ProgressBar as LegacyProgressBar,
  IconText,
  CommonModal,
  TabButton,
  TabContainer,
  EmptyState,
  StatCard as LegacyStatCard,
  SearchBar,
  QuickActionButton,
} from './CommonComponents';

// Surprise Me Component
export { default as SurpriseMe } from './SurpriseMe';
export type { SurpriseMeProps, SurpriseItem } from './SurpriseMe';

// Heart Toggle Component
export { default as HeartToggle } from './HeartToggle';
export type { HeartToggleProps } from './HeartToggle';

// Week Screen Layout Component
export { WeekScreenLayout } from './WeekScreenLayout';
export type { WeekScreenLayoutProps, WeekStep } from './WeekScreenLayout';

// Form Components
export {
  FormInput,
  FormButton,
  FormSection,
  FormCard,
} from './FormComponents';
export type {
  FormInputProps,
  FormButtonProps,
  FormSectionProps,
  FormCardProps,
} from './FormComponents';

// Re-export types for convenience
export type {
  // Enhanced Components Types
  GradientCardProps,
  StatCardProps,
  GradientButtonProps,
  ProgressBarProps,
  WeekSelectorProps,
  EnhancedModalProps,
  EnhancedInputProps,
  ActivityCardProps,
  StepNavigationProps,
} from './EnhancedComponents';

export type {
  // Layout Components Types
  ScreenLayoutProps,
  HeaderLayoutProps,
  ContentLayoutProps,
  CardGridProps,
  SectionLayoutProps,
  FlexRowProps,
  FlexColumnProps,
  SpacerProps,
  DividerProps,
} from './LayoutComponents';

export type {
  // Story Components Types
  StorySection,
  StoryHeaderProps,
  StoryTimelineItemProps,
  StorySectionEditorProps,
  StoryProgressBarProps,
  StoryNavigationProps,
} from './StoryComponents';
