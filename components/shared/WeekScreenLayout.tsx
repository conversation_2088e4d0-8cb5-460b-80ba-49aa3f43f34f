/**
 * Week Screen Layout Component
 * 
 * Shared layout component for all week screens to reduce duplication.
 * Provides consistent navigation, progress tracking, and styling.
 * 
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Save, CheckCircle } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { colors } from '../../utils/colors';
import { usePointsSystemSupabase } from '../../hooks/usePointsSystemSupabase';

const { width } = Dimensions.get('window');

/**
 * Step configuration for week screens
 */
export interface WeekStep {
  title: string;
  icon: React.ReactNode;
  activityName: string;
}

/**
 * Props for the WeekScreenLayout component
 */
export interface WeekScreenLayoutProps {
  /** Week number for display */
  weekNumber: number;
  /** Steps configuration for this week */
  steps: WeekStep[];
  /** Current step index */
  currentStep: number;
  /** Function to update current step */
  setCurrentStep: (step: number) => void;
  /** Completion status for each section */
  completedSections: boolean[];
  /** Function to mark section as complete */
  markSectionComplete: (index: number) => Promise<void>;
  /** Function to handle save and continue */
  onSaveAndContinue: () => Promise<void>;
  /** Function to handle coming back later */
  onComeBackLater: () => void;
  /** Children content to render */
  children: React.ReactNode;
  /** Custom header content */
  headerContent?: React.ReactNode;
  /** Points to award per activity */
  pointsPerActivity?: number;
}

/**
 * WeekScreenLayout Component
 * 
 * Provides a consistent layout and navigation for all week screens.
 * Handles step navigation, progress tracking, and points awarding.
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const WeekScreenLayout: React.FC<WeekScreenLayoutProps> = ({
  weekNumber,
  steps,
  currentStep,
  setCurrentStep,
  completedSections,
  markSectionComplete,
  onSaveAndContinue,
  onComeBackLater,
  children,
  headerContent,
  pointsPerActivity = 25,
}) => {
  const params = useLocalSearchParams();
  const { addPoints } = usePointsSystemSupabase();

  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section, steps.length, setCurrentStep]);

  const handleSaveAndContinue = async () => {
    try {
      // Mark current activity as completed and award points
      const result = await addPoints(pointsPerActivity, 'activity');
      
      if (currentStep < steps.length - 1) {
        await markSectionComplete(currentStep);
        setCurrentStep(currentStep + 1);
        
        // Show points earned
        Alert.alert(
          'Activity Completed!', 
          `Great job! You earned ${result.points} points!${result.bonusPoints > 0 ? `\n\nModule completion bonus: +${result.bonusPoints} points!` : ''}`
        );
      } else {
        // All steps completed
        await markSectionComplete(currentStep);
        
        const finalResult = await addPoints(pointsPerActivity, 'activity');
        Alert.alert(
          'Congratulations!', 
          `You've completed Week ${weekNumber}! All your responses have been saved to your scrapbook.\n\nFinal activity: +${finalResult.points} points!`
        );
      }
      
      // Call the custom save handler
      await onSaveAndContinue();
    } catch (error) {
      Alert.alert('Error', 'Failed to save progress. Please try again.');
    }
  };

  const getProgressPercentage = (): number => {
    if (steps.length === 0) return 0;
    return Math.round(((currentStep + 1) / steps.length) * 100);
  };

  const renderStepNavigation = () => (
    <View style={styles.stepNavigation}>
      {steps.map((step, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.stepButton,
            index === currentStep && styles.stepButtonActive,
            completedSections[index] && styles.stepButtonCompleted,
          ]}
          onPress={() => setCurrentStep(index)}
        >
          <View style={styles.stepIcon}>
            {completedSections[index] ? (
              <CheckCircle size={20} color={colors.white} />
            ) : (
              step.icon
            )}
          </View>
          <Text style={[
            styles.stepText,
            index === currentStep && styles.stepTextActive,
            completedSections[index] && styles.stepTextCompleted,
          ]}>
            {step.title}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderProgressBar = () => (
    <View style={styles.progressContainer}>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { width: `${getProgressPercentage()}%` }
          ]} 
        />
      </View>
      <Text style={styles.progressText}>
        {getProgressPercentage()}% Complete
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={colors.gradients.primary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={onComeBackLater}
          >
            <ArrowLeft size={24} color={colors.white} />
          </TouchableOpacity>
          
          <View style={styles.headerText}>
            <Text style={styles.weekTitle}>Week {weekNumber}</Text>
            <Text style={styles.activityTitle}>
              {steps[currentStep]?.activityName || 'Activity'}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={styles.saveButton}
            onPress={handleSaveAndContinue}
          >
            <Save size={24} color={colors.white} />
          </TouchableOpacity>
        </View>
        
        {headerContent}
        
        {renderStepNavigation()}
        {renderProgressBar()}
      </LinearGradient>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {children}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerText: {
    flex: 1,
    alignItems: 'center',
  },
  weekTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
  },
  saveButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  stepNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  stepButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: 80,
  },
  stepButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  stepButtonCompleted: {
    backgroundColor: colors.success,
  },
  stepIcon: {
    marginBottom: 8,
  },
  stepText: {
    fontSize: 12,
    color: colors.white,
    textAlign: 'center',
    fontWeight: '500',
  },
  stepTextActive: {
    fontWeight: 'bold',
  },
  stepTextCompleted: {
    fontWeight: 'bold',
  },
  progressContainer: {
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.white,
    borderRadius: 3,
  },
  progressText: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 20,
  },
});
