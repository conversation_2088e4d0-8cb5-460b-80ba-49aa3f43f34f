import React, { memo, useState, useCallback } from 'react';
import { Image, View, StyleSheet, ActivityIndicator, ImageStyle, Text } from 'react-native';
import { colors } from '../utils/colors';

interface OptimizedImageProps {
  /** Image source URI */
  source: { uri: string };
  /** Custom styles for the image */
  style?: ImageStyle;
  /** How the image should be resized */
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  /** Custom placeholder component to show while loading */
  placeholder?: React.ReactNode;
  /** Callback when image loads successfully */
  onLoad?: () => void;
  /** Callback when image fails to load */
  onError?: () => void;
  /** Show loading indicator */
  showLoading?: boolean;
  /** Show error state */
  showError?: boolean;
  /** Custom error message */
  errorMessage?: string;
}

const OptimizedImage = memo<OptimizedImageProps>(({
  source,
  style,
  resizeMode = 'cover',
  placeholder,
  onLoad,
  onError,
  showLoading = true,
  showError = true,
  errorMessage = 'Failed to load image'
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  /**
   * Handles successful image load
   */
  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  }, [onLoad]);

  /**
   * Handles image load error
   */
  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  }, [onError]);

  if (hasError && showError) {
    return (
      <View style={[styles.errorContainer, style]}>
        {placeholder || (
          <View style={styles.errorPlaceholder}>
            <Text style={styles.errorText}>📷</Text>
            <Text style={styles.errorMessage}>{errorMessage}</Text>
          </View>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={source}
        style={[styles.image, style]}
        resizeMode={resizeMode}
        onLoad={handleLoad}
        onError={handleError}
        // Performance optimizations
        fadeDuration={200}
        progressiveRenderingEnabled={true}
        cache="force-cache"
      />
      {isLoading && showLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="small" color={colors.primary} />
        </View>
      )}
    </View>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    backgroundColor: colors.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors.borderLight,
    borderRadius: 8,
  },
  errorPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 24,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

export default OptimizedImage;
