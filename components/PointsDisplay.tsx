/**
 * PointsDisplay Component
 * 
 * A unified component that combines the functionality of PointsDisplay, PointsDisplayV2,
 * and AchievementCelebration. Displays points with optional completion status, 
 * customizable sizing, and achievement celebrations.
 * 
 * <AUTHOR> Us Team
 * @version 3.0.0
 */

import React, { memo, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Star, CheckCircle, Trophy, Sparkles } from 'lucide-react-native';
import { colors } from '../utils/colors';

const { width, height } = Dimensions.get('window');

/**
 * Props for the PointsDisplay component
 */
interface PointsDisplayProps {
  /** Number of points to display */
  points: number;
  /** Whether the activity is completed */
  completed?: boolean;
  /** Whether to show the completion icon */
  showCompletionIcon?: boolean;
  /** Size variant for the display */
  size?: 'small' | 'medium' | 'large';
  /** Whether to show achievement celebration */
  showCelebration?: boolean;
  /** Achievement data for celebration */
  achievement?: {
    title: string;
    description: string;
    points: number;
    type: 'activity' | 'module' | 'streak' | 'milestone';
    icon?: string;
  };
  /** Callback when celebration completes */
  onCelebrationComplete?: () => void;
  /** Whether to show level information */
  showLevel?: boolean;
  /** Current level */
  level?: number;
  /** Points to next level */
  pointsToNextLevel?: number;
  /** Level progress (0-1) */
  levelProgress?: number;
  /** Whether to show progress indicator */
  showProgress?: boolean;
  /** Current progress value */
  current?: number;
  /** Total progress value */
  total?: number;
  /** Progress theme */
  progressTheme?: 'default' | 'romantic' | 'playful' | 'reflective';
  /** Progress label */
  progressLabel?: string;
}

/**
 * Memoized PointsDisplay component for performance optimization
 */
export const PointsDisplay = memo<PointsDisplayProps>(({
  points,
  completed = false,
  showCompletionIcon = false,
  size = 'medium',
  showCelebration = false,
  achievement,
  onCelebrationComplete,
  showLevel = false,
  level = 1,
  pointsToNextLevel = 0,
  levelProgress = 0,
  showProgress = false,
  current = 0,
  total = 1,
  progressTheme = 'default',
  progressLabel = 'Progress'
}) => {
  // Animation refs
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const confettiAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(height)).current;

  /**
   * Returns size-specific styles based on the size prop
   * @returns Object containing fontSize and iconSize
   */
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return { fontSize: 12, iconSize: 12, containerPadding: 8 };
      case 'large':
        return { fontSize: 18, iconSize: 20, containerPadding: 16 };
      default:
        return { fontSize: 14, iconSize: 16, containerPadding: 12 };
    }
  };

  const { fontSize, iconSize, containerPadding } = getSizeStyles();

  /**
   * Get theme colors for progress indicator
   */
  const getProgressThemeColors = () => {
    switch (progressTheme) {
      case 'romantic':
        return {
          background: colors.gradients.modulePink[0],
          fill: colors.gradients.modulePink[1],
          text: colors.white
        };
      case 'playful':
        return {
          background: colors.gradients.moduleOrange[0],
          fill: colors.gradients.moduleOrange[1],
          text: colors.white
        };
      case 'reflective':
        return {
          background: colors.gradients.moduleBlue[0],
          fill: colors.gradients.moduleBlue[1],
          text: colors.white
        };
      default:
        return {
          background: colors.gradients.primary[0],
          fill: colors.gradients.primary[1],
          text: colors.white
        };
    }
  };

  const progressThemeColors = getProgressThemeColors();
  const progressPercentage = total > 0 ? (current / total) * 100 : 0;

  // Celebration animation effect
  useEffect(() => {
    if (showCelebration && achievement) {
      // Start celebration animation
      Animated.sequence([
        // Slide in from bottom
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
        // Scale up with bounce
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        // Fade in
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        // Confetti animation
        Animated.timing(confettiAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        // Hold for a moment
        Animated.delay(1000),
        // Fade out
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
        // Slide out
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start(() => {
        onCelebrationComplete?.();
      });
    }
  }, [showCelebration, achievement]);

  /**
   * Renders the progress indicator
   */
  const renderProgressIndicator = () => {
    if (!showProgress) return null;

    return (
      <View style={styles.progressContainer}>
        {/* Progress Bar */}
        <View style={[styles.progressBar, { backgroundColor: progressThemeColors.background }]}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${progressPercentage}%`,
                backgroundColor: progressThemeColors.fill
              }
            ]} 
          />
        </View>
        
        {/* Progress Text */}
        <Text style={[styles.progressText, { color: progressThemeColors.text, fontSize: fontSize - 2 }]}>
          {progressLabel} {current} of {total}
        </Text>
        
        {/* Progress Percentage */}
        <Text style={[styles.progressPercentage, { color: progressThemeColors.text, fontSize: fontSize - 4 }]}>
          {Math.round(progressPercentage)}%
        </Text>
      </View>
    );
  };

  /**
   * Renders the main points display
   */
  const renderPointsDisplay = () => (
    <View style={[styles.container, { padding: containerPadding }]}>
      <View style={styles.pointsContainer}>
        <Star size={iconSize} color={colors.goldenAmber} />
        <Text style={[styles.pointsText, { fontSize }]}>
          {points} points
        </Text>
      </View>
      
      {showCompletionIcon && completed && (
        <View style={styles.completionContainer}>
          <CheckCircle size={iconSize} color={colors.success} />
          <Text style={[styles.completionText, { fontSize }]}>
            Completed
          </Text>
        </View>
      )}

      {showLevel && (
        <View style={styles.levelContainer}>
          <Trophy size={iconSize} color={colors.primary} />
          <Text style={[styles.levelText, { fontSize: fontSize - 2 }]}>
            Level {level}
          </Text>
          {pointsToNextLevel > 0 && (
            <Text style={[styles.nextLevelText, { fontSize: fontSize - 4 }]}>
              {pointsToNextLevel} to next level
            </Text>
          )}
        </View>
      )}
    </View>
  );

  /**
   * Renders the achievement celebration overlay
   */
  const renderCelebration = () => {
    if (!showCelebration || !achievement) return null;

    return (
      <Animated.View
        style={[
          styles.celebrationOverlay,
          {
            transform: [
              { translateY: slideAnim },
              { scale: scaleAnim }
            ],
            opacity: opacityAnim
          }
        ]}
      >
        <LinearGradient
          colors={colors.gradients.warm}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.celebrationGradient}
        >
          {/* Confetti Animation */}
          <Animated.View
            style={[
              styles.confettiContainer,
              {
                opacity: confettiAnim,
                transform: [
                  {
                    scale: confettiAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.5, 1.2]
                    })
                  }
                ]
              }
            ]}
          >
            <Sparkles size={32} color={colors.white} />
          </Animated.View>

          {/* Achievement Content */}
          <View style={styles.achievementContent}>
            <View style={styles.achievementIcon}>
              <Text style={styles.achievementEmoji}>
                {achievement.icon || '🏆'}
              </Text>
            </View>
            
            <Text style={styles.achievementTitle}>
              {achievement.title}
            </Text>
            
            <Text style={styles.achievementDescription}>
              {achievement.description}
            </Text>
            
            <View style={styles.achievementPoints}>
              <Star size={20} color={colors.white} />
              <Text style={styles.achievementPointsText}>
                +{achievement.points} points
              </Text>
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  return (
    <View style={styles.wrapper}>
      {renderProgressIndicator()}
      {renderPointsDisplay()}
      {renderCelebration()}
    </View>
  );
});

PointsDisplay.displayName = 'PointsDisplay';

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
  },
  progressContainer: {
    alignItems: 'center',
    width: '100%',
    marginBottom: 12,
  },
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontWeight: '600',
    marginBottom: 4,
  },
  progressPercentage: {
    opacity: 0.8,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  pointsText: {
    color: colors.textPrimary,
    fontWeight: '600',
  },
  completionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  completionText: {
    color: colors.success,
    fontWeight: '500',
  },
  levelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  levelText: {
    color: colors.primary,
    fontWeight: '600',
  },
  nextLevelText: {
    color: colors.textSecondary,
    fontWeight: '400',
  },
  celebrationOverlay: {
    position: 'absolute',
    top: -200,
    left: -50,
    right: -50,
    bottom: -200,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  celebrationGradient: {
    width: width - 40,
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  confettiContainer: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  achievementContent: {
    alignItems: 'center',
    zIndex: 1,
  },
  achievementIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.whiteOverlayStrong,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  achievementEmoji: {
    fontSize: 40,
  },
  achievementTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textInverse,
    textAlign: 'center',
    marginBottom: 8,
  },
  achievementDescription: {
    fontSize: 16,
    color: colors.textInverse,
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.9,
    lineHeight: 22,
  },
  achievementPoints: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.whiteOverlayMedium,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  achievementPointsText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textInverse,
  },
});