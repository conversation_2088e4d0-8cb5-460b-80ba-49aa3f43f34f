# 🚀 Everlasting Us - Build & Development Guide

## 📱 Quick Start Commands

### Development Mode
```bash
# Start development server
npm run dev

# Or use Expo CLI directly
npx expo start

# Start with specific platform
npx expo start --ios
npx expo start --android
npx expo start --web
```

### Building for Production
```bash
# Build for web
npm run build:web

# Build for iOS
npx expo run:ios

# Build for Android
npx expo run:android

# Build all platforms
npx expo export
```

## 🛠️ Development Workflow

### 1. **Daily Development**
```bash
# Start development server
npm run dev

# This will:
# - Start Metro bundler
# - Open Expo DevTools in browser
# - Show QR code for mobile testing
# - Enable hot reloading
```

### 2. **Testing on Devices**
```bash
# iOS Simulator (macOS only)
npx expo start --ios

# Android Emulator
npx expo start --android

# Physical device via QR code
npx expo start
# Then scan QR code with Expo Go app
```

### 3. **Web Development**
```bash
# Start web development
npx expo start --web

# Build optimized web version
npm run build:web

# Preview built web version
npx serve dist
```

### 4. **Cache Management**
```bash
# Daily development - usually no cache clearing needed
npm run dev

# If issues arise - clear Metro cache
npx expo start --clear

# For stubborn issues - full cache reset
npx expo start --clear --reset-cache

# Nuclear option - fresh start
rm -rf node_modules && npm install
npx expo start --clear
```

## 📦 Build Commands Explained

### **Development Builds**
- **`npm run dev`** - Starts development server with hot reloading
- **`npx expo start`** - Same as above, opens Expo DevTools
- **`npx expo start --web`** - Web development mode

### **Production Builds**
- **`npm run build:web`** - Creates optimized web build in `dist/` folder
- **`npx expo export`** - Exports for all platforms
- **`npx expo run:ios`** - Builds and runs iOS app
- **`npx expo run:android`** - Builds and runs Android app

### **Platform-Specific Builds**
```bash
# iOS
npx expo run:ios --configuration Release

# Android
npx expo run:android --variant release

# Web
npx expo export --platform web
```

## 🔧 Environment Setup

### **Prerequisites**
```bash
# Install Node.js (v18+ recommended)
node --version

# Install Expo CLI globally (optional)
npm install -g @expo/cli

# Install project dependencies
npm install
```

### **Environment Variables**
```bash
# Create .env file for local development
cp .env.example .env

# Set environment variables
EXPO_NO_TELEMETRY=1
```

## 📱 Testing Best Practices

### **1. Multi-Platform Testing**
```bash
# Test on all platforms during development
npx expo start --ios
npx expo start --android  
npx expo start --web
```

### **2. Device Testing**
- **iOS**: Use iOS Simulator or physical device with Expo Go
- **Android**: Use Android Emulator or physical device with Expo Go
- **Web**: Test in multiple browsers (Chrome, Safari, Firefox)

### **3. Performance Testing**
```bash
# Check bundle size
npx expo export --platform web --analyze

# Monitor performance in development
npx expo start --dev-client
```

## 🚀 Deployment

### **Web Deployment**
```bash
# Build for production
npm run build:web

# Deploy to various platforms:
# - Vercel: npx vercel dist
# - Netlify: npx netlify deploy --dir dist
# - GitHub Pages: Copy dist/ contents to gh-pages branch
```

### **Mobile App Store Deployment**
```bash
# Build for app stores
npx expo build:ios
npx expo build:android

# Or use EAS Build (recommended)
npx eas build --platform ios
npx eas build --platform android
```

## 🐛 Troubleshooting

### **Cache Management & Clearing**

#### **When to Clear Cache**
- Build failures or unexpected errors
- Stale data or outdated components
- Performance issues or slow builds
- Dependency conflicts
- Metro bundler issues
- Hot reload not working properly

#### **Metro Bundler Cache Issues**
```bash
# Clear Metro cache (most common)
npx expo start --clear

# Reset Expo cache completely
npx expo r -c

# Force clear all caches
npx expo start --clear --reset-cache

# Clear watchman cache (if using watchman)
watchman watch-del-all
```

#### **NPM & Package Cache Issues**
```bash
# Clear npm cache
npm cache clean --force

# Clear yarn cache (if using yarn)
yarn cache clean

# Remove node_modules and reinstall
rm -rf node_modules
rm package-lock.json
npm install

# Clear Expo CLI cache
npx expo install --fix
```

#### **React Native Cache Issues**
```bash
# Clear React Native cache
npx react-native start --reset-cache

# Clear iOS build cache
cd ios && rm -rf build && cd ..

# Clear Android build cache
cd android && ./gradlew clean && cd ..

# Clear Expo build cache
npx expo build:clean
```

#### **Device & Simulator Cache**
```bash
# iOS Simulator
# Hardware > Erase All Content and Settings

# Android Emulator
# Settings > Apps > Expo Go > Storage > Clear Data

# Physical Device
# Uninstall and reinstall Expo Go app
```

#### **Build Failures**
```bash
# Check for TypeScript errors
npx tsc --noEmit

# Lint code
npm run lint

# Check dependencies
npm audit
```

#### **Performance Issues**
```bash
# Analyze bundle
npx expo export --platform web --analyze

# Check for large dependencies
npx expo install --fix
```

## 📊 Development Tools

### **Recommended VS Code Extensions**
- **Expo Tools** - Expo development support
- **React Native Tools** - React Native development
- **TypeScript Importer** - Auto-import TypeScript
- **ESLint** - Code quality
- **Prettier** - Code formatting

### **Browser DevTools**
- **React Developer Tools** - React component inspection
- **Redux DevTools** - State management debugging
- **Network Tab** - API call monitoring

## 🔄 Git Workflow

### **Branch Strategy**
```bash
# Create feature branch
git checkout -b feature/week-two-bingo

# Development workflow
git add .
git commit -m "feat: add strengths bingo grid"
git push origin feature/week-two-bingo

# Merge to main
git checkout main
git merge feature/week-two-bingo
```

### **Commit Convention**
```
feat: new feature
fix: bug fix
docs: documentation
style: formatting
refactor: code restructuring
test: adding tests
chore: maintenance
```

## 📱 Platform-Specific Considerations

### **iOS**
- Test on different iOS versions (14+)
- Check Safe Area handling
- Test with different screen sizes
- Verify App Store guidelines compliance

### **Android**
- Test on different Android versions (8+)
- Check permissions handling
- Test with different screen densities
- Verify Google Play guidelines compliance

### **Web**
- Test responsive design
- Check browser compatibility
- Verify PWA features
- Test offline functionality

## 🚀 Performance Optimization

### **Bundle Optimization**
```bash
# Analyze bundle size
npx expo export --platform web --analyze

# Tree shake unused code
npx expo export --platform web --minify

# Enable compression
npx expo export --platform web --compress
```

### **Runtime Performance**
- Use React.memo for expensive components
- Implement lazy loading for routes
- Optimize images and assets
- Monitor memory usage

## 📋 Pre-Launch Checklist

### **Code Quality**
- [ ] All TypeScript errors resolved
- [ ] ESLint passes without warnings
- [ ] Prettier formatting applied
- [ ] Unit tests passing
- [ ] Integration tests passing

### **Cache & Build Cleanliness**
- [ ] Metro cache cleared if issues occurred
- [ ] Node modules are up to date
- [ ] Build cache is clean
- [ ] No stale dependencies
- [ ] Fresh build completed successfully

### **Performance**
- [ ] Bundle size optimized
- [ ] Loading times acceptable
- [ ] Memory usage optimized
- [ ] Battery usage optimized (mobile)

### **Platform Testing**
- [ ] iOS testing completed
- [ ] Android testing completed
- [ ] Web testing completed
- [ ] Cross-browser compatibility verified

### **User Experience**
- [ ] Navigation flows tested
- [ ] Error handling verified
- [ ] Accessibility features implemented
- [ ] Localization completed

## 🎯 Quick Reference

### **Daily Commands**
```bash
npm run dev          # Start development
npm run build:web    # Build for web
npm run lint         # Check code quality
```

### **Troubleshooting & Cache Clearing**
```bash
# Metro & Expo Cache
npx expo start --clear           # Clear Metro cache
npx expo r -c                   # Reset Expo cache
npx expo start --clear --reset-cache  # Force clear all

# Package Cache
npm cache clean --force          # Clear npm cache
rm -rf node_modules && npm install  # Fresh install

# Build Cache
npx expo build:clean            # Clear build cache
cd ios && rm -rf build && cd .. # Clear iOS cache
cd android && ./gradlew clean   # Clear Android cache
```

### **Build Commands**
```bash
npm run build:web        # Web production build
npx expo run:ios        # iOS build & run
npx expo run:android    # Android build & run
```

---

## 🆘 Need Help?

### **Resources**
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [Expo Discord](https://chat.expo.dev/)
- [GitHub Issues](https://github.com/expo/expo/issues)

### **Common Commands Cheat Sheet**
```bash
# Development
npm run dev                    # Start dev server
npx expo start               # Start Expo
npx expo start --web         # Web mode
npx expo start --ios         # iOS mode
npx expo start --android     # Android mode

# Building
npm run build:web            # Build web
npx expo export              # Export all
npx expo run:ios            # Build & run iOS
npx expo run:android        # Build & run Android

# Cache Management (in order of severity)
npx expo start --clear       # Clear Metro cache (most common)
npx expo r -c                # Reset Expo cache
npx expo start --clear --reset-cache  # Force clear all
npm cache clean --force       # Clear npm cache
rm -rf node_modules && npm install  # Nuclear option
```

*Happy coding! 🚀*
