@echo off
chcp 65001 >nul
echo 🔍 Everlasting Us - Auto-Debug Script
echo =====================================
echo.

echo 📋 Checking System Environment...
echo --------------------------------

REM Check Node.js version
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js: %%i
) else (
    echo ❌ Node.js not found
    pause
    exit /b 1
)

REM Check npm version
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ npm: %%i
) else (
    echo ❌ npm not found
    pause
    exit /b 1
)

REM Check Expo CLI
npx expo --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npx expo --version') do echo ✅ Expo CLI: %%i
) else (
    echo ⚠️  Expo CLI not found or not working
)

echo.
echo 📁 Checking Project Structure...
echo -------------------------------

REM Check if we're in the right directory
if exist "package.json" (
    echo ✅ Found package.json
) else (
    echo ❌ package.json not found - are you in the right directory?
    pause
    exit /b 1
)

REM Check app directory
if exist "app" (
    echo ✅ Found app/ directory
) else (
    echo ❌ app/ directory not found
)

REM Check components directory
if exist "components" (
    echo ✅ Found components/ directory
) else (
    echo ⚠️  components/ directory not found
)

REM Check hooks directory
if exist "hooks" (
    echo ✅ Found hooks/ directory
) else (
    echo ⚠️  hooks/ directory not found
)

echo.
echo 📦 Checking Dependencies...
echo ---------------------------

REM Check if node_modules exists
if exist "node_modules" (
    echo ✅ node_modules/ directory exists
) else (
    echo ❌ node_modules/ directory not found - run 'npm install' first
    pause
    exit /b 1
)

echo.
echo 🔧 Checking Configuration Files...
echo --------------------------------

REM Check metro config
if exist "metro.config.js" (
    echo ✅ Found metro.config.js
    echo Metro config contents:
    type metro.config.js | findstr /n . | findstr "^1:" | findstr /v "//"
    echo ...
) else (
    echo ⚠️  metro.config.js not found - using default Metro config
)

REM Check app.json or expo.json
if exist "app.json" (
    echo ✅ Found app.json
) else if exist "expo.json" (
    echo ✅ Found expo.json
) else (
    echo ❌ Neither app.json nor expo.json found
)

REM Check TypeScript config
if exist "tsconfig.json" (
    echo ✅ Found tsconfig.json
) else (
    echo ⚠️  tsconfig.json not found
)

echo.
echo 🐛 Running Code Quality Checks...
echo --------------------------------

REM Check TypeScript compilation
echo Checking TypeScript compilation...
npx tsc --noEmit >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ TypeScript compilation successful
) else (
    echo ❌ TypeScript compilation failed:
    npx tsc --noEmit 2>&1 | findstr /n . | findstr "^1:" | findstr /v "//"
)

echo.
echo 🌐 Checking Web Build...
echo ------------------------

REM Check if web build works
echo Attempting web build...
npm run build:web >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Web build successful
) else (
    echo ❌ Web build failed
    echo Build error output:
    npm run build:web 2>&1 | findstr /n . | findstr "^1:" | findstr /v "//"
)

echo.
echo 🔌 Checking Port Usage...
echo ------------------------

REM Check what's using port 8081
echo Checking port 8081...
netstat -an | findstr ":8081" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  Port 8081 is in use:
    netstat -an | findstr ":8081"
) else (
    echo ✅ Port 8081 is available
)

REM Check what's using port 19006 (Expo web default)
echo Checking port 19006...
netstat -an | findstr ":19006" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  Port 19006 is in use:
    netstat -an | findstr ":19006"
) else (
    echo ✅ Port 19006 is available
)

echo.
echo 🧹 Cache Status...
echo ------------------

REM Check cache sizes
echo Checking cache sizes...
if exist ".expo" (
    echo ℹ️  .expo cache directory found
) else (
    echo ℹ️  No .expo cache directory found
)

if exist "node_modules\.cache" (
    echo ℹ️  npm cache directory found
) else (
    echo ℹ️  No npm cache directory found
)

echo.
echo 📊 Summary ^& Recommendations...
echo ===============================

echo.
echo 🔍 Next Steps:
echo 1. If you see any ❌ errors above, fix those first
echo 2. If you see ⚠️  warnings, consider addressing them
echo 3. Try starting the app: npx expo start --web
echo 4. If still blank, check browser console for JavaScript errors
echo 5. Try clearing caches: npx expo start --clear --reset-cache

echo.
echo 🌐 For Web Development:
echo - Use: npx expo start --web ^(not just npx expo start^)
echo - This should open http://localhost:19006, not 8081
echo - Port 8081 is Metro bundler, 19006 is the web app

echo.
echo 📱 For Mobile Development:
echo - Use: npx expo start
echo - Scan QR code with Expo Go app

echo.
echo 🚀 Debug script completed!
echo Check the output above for any issues.

pause
