import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../utils/logger';

export interface DreamVacationAdjectives {
  playerOneAdjectives: string[];
  playerTwoAdjectives: string[];
  timestamp: number;
}

export interface DreamVacationPlan {
  when: string;
  where: string;
  theme: string;
  worldLocation: string;
  vibe: string;
  companions: string;
  accommodation: string;
  dailyActivities: string;
  weather: string;
  activities: string;
  food: string;
  splurgeLevel: string;
  bucketListItem: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface ConflictStyleReflection {
  playerOneStyles: string[];
  playerTwoStyles: string[];
  playerOneReflection: string;
  playerTwoReflection: string;
  alternativeStyle: string;
  experimentFeelings: string;
  timestamp: number;
}

export interface WeekFiveData {
  dreamVacationAdjectives: DreamVacationAdjectives;
  dreamVacationPlan: DreamVacationPlan;
  chatPrompts: ChatPrompt[];
  conflictStyleReflection: ConflictStyleReflection;
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_FIVE_STORAGE_KEY = 'week_five_data';

export const useWeekFiveData = () => {
  const [data, setData] = useState<WeekFiveData>({
    dreamVacationAdjectives: {
      playerOneAdjectives: ['', '', '', '', ''],
      playerTwoAdjectives: ['', '', '', '', ''],
      timestamp: Date.now(),
    },
    dreamVacationPlan: {
      when: '',
      where: '',
      theme: '',
      worldLocation: '',
      vibe: '',
      companions: '',
      accommodation: '',
      dailyActivities: '',
      weather: '',
      activities: '',
      food: '',
      splurgeLevel: '',
      bucketListItem: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'Name someone you wish you were closer to. Why?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'What belief about yourself no longer serves you?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    conflictStyleReflection: {
      playerOneStyles: [],
      playerTwoStyles: [],
      playerOneReflection: '',
      playerTwoReflection: '',
      alternativeStyle: '',
      experimentFeelings: '',
      timestamp: Date.now(),
    },
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekFiveData>(WEEK_FIVE_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Five data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekFiveData) => {
    try {
      await secureStorage.setItem(WEEK_FIVE_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Five data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateDreamVacationAdjectives = async (player: 'playerOne' | 'playerTwo', adjectives: string[]) => {
    const newData = { ...data };
    if (player === 'playerOne') {
      newData.dreamVacationAdjectives.playerOneAdjectives = adjectives.map(adj => {
        const result = sanitizeText(adj);
        return result.isValid ? result.sanitizedValue! : adj;
      });
    } else {
      newData.dreamVacationAdjectives.playerTwoAdjectives = adjectives.map(adj => {
        const result = sanitizeText(adj);
        return result.isValid ? result.sanitizedValue! : adj;
      });
    }
    newData.dreamVacationAdjectives.timestamp = Date.now();
    await saveData(newData);
  };

  const updateDreamVacationPlan = async (plan: Partial<DreamVacationPlan>) => {
    const newData = { ...data };
    newData.dreamVacationPlan = { ...newData.dreamVacationPlan, ...plan, timestamp: Date.now() };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, player: 'playerOne' | 'playerTwo', answer: string) => {
    const newData = { ...data };
    if (player === 'playerOne') {
      const result = sanitizeText(answer);
      newData.chatPrompts[index].playerOneAnswer = result.isValid ? result.sanitizedValue! : answer;
    } else {
      const result = sanitizeText(answer);
      newData.chatPrompts[index].playerTwoAnswer = result.isValid ? result.sanitizedValue! : answer;
    }
    newData.chatPrompts[index].timestamp = Date.now();
    await saveData(newData);
  };

  const updateConflictStyleReflection = async (reflection: Partial<ConflictStyleReflection>) => {
    const newData = { ...data };
    newData.conflictStyleReflection = { ...newData.conflictStyleReflection, ...reflection, timestamp: Date.now() };
    await saveData(newData);
  };

  const updateCompletedSections = async (sections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = sections;
    if (sections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekFiveData = {
      dreamVacationAdjectives: {
        playerOneAdjectives: ['', '', '', '', ''],
        playerTwoAdjectives: ['', '', '', '', ''],
        timestamp: Date.now(),
      },
      dreamVacationPlan: {
        when: '',
        where: '',
        theme: '',
        worldLocation: '',
        vibe: '',
        companions: '',
        accommodation: '',
        dailyActivities: '',
        weather: '',
        activities: '',
        food: '',
        splurgeLevel: '',
        bucketListItem: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'Name someone you wish you were closer to. Why?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'What belief about yourself no longer serves you?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      conflictStyleReflection: {
        playerOneStyles: [],
        playerTwoStyles: [],
        playerOneReflection: '',
        playerTwoReflection: '',
        alternativeStyle: '',
        experimentFeelings: '',
        timestamp: Date.now(),
      },
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateDreamVacationAdjectives,
    updateDreamVacationPlan,
    updateChatPrompt,
    updateConflictStyleReflection,
    updateCompletedSections,
    resetData,
  };
};
