/**
 * Home Screen Hook
 * 
 * Custom hook for managing home screen state and logic.
 * This separates business logic from UI components for better maintainability.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { secureStorage } from '../utils/secureStorage';

// ============================================================================
// TYPES
// ============================================================================

export interface CustomGoal {
  id: string;
  title: string;
  completed: boolean;
  weekNumber: number;
}

export interface WeekActivity {
  title: string;
  route: string;
  category: string;
}

export interface Goal {
  id: string;
  title: string;
  completed: boolean;
  isModuleGoal: boolean;
  category: string;
  route: string;
  action: () => void;
}

// ============================================================================
// HOOK
// ============================================================================

export const useHomeScreen = () => {
  // State
  const [currentWeek, setCurrentWeek] = useState(1);
  const [showAddGoalModal, setShowAddGoalModal] = useState(false);
  const [newGoalText, setNewGoalText] = useState('');
  const [customGoals, setCustomGoals] = useState<CustomGoal[]>([]);

  // Week activities data (in a real app, this would come from a service)
  const weekActivities: Record<number, WeekActivity[]> = {
    1: [
      { title: 'Complete The Match Game', route: '/week-one?section=0', category: 'Getting to Know You' },
      { title: 'Plan Date Night', route: '/week-one?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-one?section=2', category: 'Communication' },
      { title: 'Practice Soft Start-Up', route: '/week-one?section=3', category: 'Relationship Skills' }
    ],
    2: [
      { title: 'Complete Strengths Bingo', route: '/week-two?section=0', category: 'Strengths & Positivity' },
      { title: 'Plan Date Night', route: '/week-two?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-two?section=2', category: 'Communication' },
      { title: 'Practice 5:1 Ratio', route: '/week-two?section=3', category: 'Relationship Skills' }
    ],
    3: [
      { title: 'Play Would You Rather?', route: '/week-three?section=0', category: 'Fun & Discovery' },
      { title: 'Plan Alphabet Date Night', route: '/week-three?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-three?section=2', category: 'Communication' },
      { title: 'Practice Being Curious', route: '/week-three?section=3', category: 'Relationship Skills' }
    ],
    4: [
      { title: 'Create a Playlist', route: '/week-four?section=0', category: 'Creative Connection' },
      { title: 'Plan Movie Theme Night', route: '/week-four?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-four?section=2', category: 'Communication' },
      { title: 'Practice Emotional Regulation', route: '/week-four?section=3', category: 'Relationship Skills' }
    ],
    5: [
      { title: 'Plan Dream Vacation', route: '/week-five?section=0', category: 'Dreams & Planning' },
      { title: 'Plan Date Night', route: '/week-five?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-five?section=2', category: 'Communication' },
      { title: 'Learn Conflict Style', route: '/week-five?section=3', category: 'Relationship Skills' }
    ],
    6: [
      { title: 'Share Childhood Memories', route: '/week-six?section=0', category: 'Memories & Connection' },
      { title: 'Plan Memory Lane Date', route: '/week-six?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-six?section=2', category: 'Communication' },
      { title: 'Practice Validation Toolkit', route: '/week-six?section=3', category: 'Relationship Skills' }
    ],
    7: [
      { title: 'Create Superhero Duo Chart', route: '/week-seven?section=0', category: 'Fun & Creativity' },
      { title: 'Plan Thrift Shop Showdown', route: '/week-seven?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-seven?section=2', category: 'Communication' },
      { title: 'Practice Turning Toward', route: '/week-seven?section=3', category: 'Relationship Skills' }
    ],
    8: [
      { title: 'Play Perfect Saturday Game', route: '/week-eight?section=0', category: 'Dreams & Planning' },
      { title: 'Plan Blended Perfect Day', route: '/week-eight?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-eight?section=2', category: 'Communication' },
      { title: 'Learn Conflict Mapping', route: '/week-eight?section=3', category: 'Relationship Skills' }
    ],
    9: [
      { title: 'Design Family Crest', route: '/week-nine?section=0', category: 'Values & Identity' },
      { title: 'Plan Live Show Date', route: '/week-nine?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-nine?section=2', category: 'Communication' },
      { title: 'Explore Shared Values', route: '/week-nine?section=3', category: 'Relationship Skills' }
    ],
    10: [
      { title: 'Plan Dream Bank ($5M)', route: '/week-ten?section=0', category: 'Financial Dreams' },
      { title: 'Plan Get Artsy Date', route: '/week-ten?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-ten?section=2', category: 'Communication' },
      { title: 'Have Money Talk', route: '/week-ten?section=3', category: 'Relationship Skills' }
    ],
    11: [
      { title: 'Take Love Languages Quiz', route: '/week-eleven?section=0', category: 'Love & Understanding' },
      { title: 'Plan Love Language Date', route: '/week-eleven?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-eleven?section=2', category: 'Communication' },
      { title: 'Practice Appreciation', route: '/week-eleven?section=3', category: 'Relationship Skills' }
    ],
    12: [
      { title: 'Create Future Vision Board', route: '/week-twelve?section=0', category: 'Future & Dreams' },
      { title: 'Plan Vision Date Night', route: '/week-twelve?section=1', category: 'Date Planning' },
      { title: 'Complete Chat Prompts', route: '/week-twelve?section=2', category: 'Communication' },
      { title: 'Practice Growth Mindset', route: '/week-twelve?section=3', category: 'Relationship Skills' }
    ]
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const getCurrentWeekActivities = (): WeekActivity[] => {
    return weekActivities[currentWeek] || weekActivities[1];
  };

  const getWeekProgress = (weekNumber: number): number => {
    // This would typically come from a service that tracks completion
    return 0;
  };

  const isModuleActivityCompleted = (weekNumber: number, activityTitle: string): boolean => {
    // This would typically check against stored completion data
    return false;
  };

  const getAllGoals = (): Goal[] => {
    const moduleGoals: Goal[] = getCurrentWeekActivities().map(activity => ({
      id: `module-${activity.title}`,
      title: activity.title,
      completed: isModuleActivityCompleted(currentWeek, activity.title),
      isModuleGoal: true,
      category: activity.category,
      route: activity.route,
      action: () => {
        // Navigation would be handled by the component
        console.log(`Navigate to: ${activity.route}`);
      }
    }));
    
    const customGoalItems: Goal[] = customGoals.map(goal => ({
      id: goal.id,
      title: goal.title,
      completed: goal.completed,
      isModuleGoal: false,
      category: 'Custom Goal',
      route: '',
      action: () => toggleGoalCompletion(goal.id)
    }));
    
    return [...moduleGoals, ...customGoalItems];
  };

  const getGreeting = (): string => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getNextMilestone = () => {
    const currentPoints = 0; // This would come from the points system
    const milestones = [100, 250, 500, 750, 1000, 1500, 2000];
    const next = milestones.find(m => m > currentPoints);
    
    if (!next) return { points: 0, text: 'All milestones achieved!' };
    
    const remaining = next - currentPoints;
    return {
      points: next,
      text: `Complete ${Math.ceil(remaining / 25)} more activities to reach ${next} points!`
    };
  };

  // ============================================================================
  // ACTIONS
  // ============================================================================

  const loadCustomGoals = async (): Promise<void> => {
    try {
      const storedGoals = await secureStorage.getItem<CustomGoal[]>('custom_goals');
      if (storedGoals) {
        setCustomGoals(storedGoals);
      }
    } catch (error) {
      console.error('Error loading custom goals:', error);
    }
  };

  const saveCustomGoals = async (goals: CustomGoal[]): Promise<void> => {
    try {
      await secureStorage.setItem('custom_goals', goals);
    } catch (error) {
      console.error('Error saving custom goals:', error);
    }
  };

  const addCustomGoal = (): void => {
    if (newGoalText.trim()) {
      const newGoal: CustomGoal = {
        id: Date.now().toString(),
        title: newGoalText.trim(),
        completed: false,
        weekNumber: currentWeek
      };
      const updatedGoals = [...customGoals, newGoal];
      setCustomGoals(updatedGoals);
      saveCustomGoals(updatedGoals);
      setNewGoalText('');
      setShowAddGoalModal(false);
    }
  };

  const toggleGoalCompletion = (goalId: string): void => {
    const updatedGoals = customGoals.map(goal => 
      goal.id === goalId ? { ...goal, completed: !goal.completed } : goal
    );
    setCustomGoals(updatedGoals);
    saveCustomGoals(updatedGoals);
  };

  const removeCustomGoal = (goalId: string): void => {
    const updatedGoals = customGoals.filter(goal => goal.id !== goalId);
    setCustomGoals(updatedGoals);
    saveCustomGoals(updatedGoals);
  };

  const openAddGoalModal = (): void => {
    setShowAddGoalModal(true);
  };

  const closeAddGoalModal = (): void => {
    setShowAddGoalModal(false);
    setNewGoalText('');
  };

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    loadCustomGoals();
  }, []);

  // ============================================================================
  // RETURN
  // ============================================================================

  return {
    // State
    currentWeek,
    setCurrentWeek,
    showAddGoalModal,
    newGoalText,
    setNewGoalText,
    customGoals,
    
    // Computed values
    getCurrentWeekActivities,
    getWeekProgress,
    getAllGoals,
    getGreeting,
    getNextMilestone,
    
    // Actions
    addCustomGoal,
    toggleGoalCompletion,
    removeCustomGoal,
    openAddGoalModal,
    closeAddGoalModal,
  };
};
