import { useEffect, useCallback } from 'react';
import { useDateNightIdeasSupabase } from './useDateNightIdeasSupabase';
import { useWeekOneData } from './useWeekOneData';
import { useWeekTwoData } from './useWeekTwoData';
import { useWeekThreeData } from './useWeekThreeData';
import { useWeekFourData } from './useWeekFourData';
import { useWeekFiveData } from './useWeekFiveData';
import { useWeekSixData } from './useWeekSixData';
import { useWeekSevenData } from './useWeekSevenData';
import { useWeekEightData } from './useWeekEightData';
import { useWeekNineData } from './useWeekNineData';
import { useWeekTenData } from './useWeekTenData';
import { useWeekElevenData } from './useWeekElevenData';
import { useWeekTwelveData } from './useWeekTwelveData';
import { logger } from '../utils/logger';

export const useWeeklyDateNightIntegration = () => {
  const { 
    allIdeas: ideas, 
    saveUserIdea, 
    refreshData: refreshDateNightPool 
  } = useDateNightIdeasSupabase();

  // Import all week data hooks
  const weekDataHooks = [
    useWeekOneData(),
    useWeekTwoData(),
    useWeekThreeData(),
    useWeekFourData(),
    useWeekFiveData(),
    useWeekSixData(),
    useWeekSevenData(),
    useWeekEightData(),
    useWeekNineData(),
    useWeekTenData(),
    useWeekElevenData(),
    useWeekTwelveData()
  ];

  // Map of week numbers to their Date Night activity names
  const weekDateNightMap = {
    1: 'Date Night Plan',
    2: 'Date Night Plan',
    3: 'Alphabet Date Night',
    4: 'Movie Theme Night',
    5: 'Date Night Plan',
    6: 'Memory Lane Date',
    7: 'Thrift Shop Showdown',
    8: 'Blended Perfect Day',
    9: 'Live Show Date',
    10: 'Get Artsy Date',
    11: 'Mini-Olympics',
    12: 'Get Active Date'
  };

  // Check if a weekly Date Night is completed
  const isWeeklyDateNightCompleted = useCallback((weekNumber: number): boolean => {
    if (weekNumber < 1 || weekNumber > 12) return false;
    
    const weekData = weekDataHooks[weekNumber - 1]?.data;
    if (!weekData) return false;

    // Check if the Date Night section is completed
    const dateNightSectionIndex = 1; // Date Night is typically section 1 in weekly modules
    return weekData.completedSections?.[dateNightSectionIndex] || false;
  }, [weekDataHooks]);

  // Sync weekly Date Night completion status with global pool
  const syncWeeklyDateNightStatus = useCallback(async () => {
    try {
      const weeklyIdeas = ideas.filter(idea => idea.source === 'weekly');
      
      for (const idea of weeklyIdeas) {
        if (!idea.weekNumber) continue;
        
        const isCompleted = isWeeklyDateNightCompleted(idea.weekNumber);
        const shouldBeCompleted = idea.status === 'completed';
        
        // If weekly module shows completed but pool doesn't, update pool
        if (isCompleted && !shouldBeCompleted) {
          await saveUserIdea(idea.id, 'completed');
        }
        // If weekly module shows not completed but pool shows completed, update pool
        else if (!isCompleted && shouldBeCompleted) {
          await saveUserIdea(idea.id, 'planned');
        }
      }
    } catch (error) {
      logger.error('Error syncing weekly Date Night status:', error);
    }
  }, [ideas, isWeeklyDateNightCompleted, saveUserIdea]);

  // Mark a weekly Date Night as completed in both systems
  const completeWeeklyDateNight = useCallback(async (weekNumber: number) => {
    try {
      // Find the corresponding idea in the pool
      const weeklyIdea = ideas.find(idea => 
        idea.source === 'weekly' && idea.weekNumber === weekNumber
      );
      
      if (weeklyIdea) {
        // Update the global pool
        await saveUserIdea(weeklyIdea.id, 'completed');
      }
      
      // Note: The weekly module completion should be handled by the existing weekly logic
      // This hook just ensures the global pool stays in sync
    } catch (error) {
      logger.error('Error completing weekly Date Night:', error);
    }
  }, [ideas, saveUserIdea]);

  // Get completion status for a specific week's Date Night
  const getWeeklyDateNightStatus = useCallback((weekNumber: number) => {
    const weeklyIdea = ideas.find(idea => 
      idea.source === 'weekly' && idea.weekNumber === weekNumber
    );
    
    return {
      isCompleted: weeklyIdea?.status === 'completed',
      completedAt: weeklyIdea?.completedAt,
      idea: weeklyIdea
    };
  }, [ideas]);

  // Get all completed weekly Date Nights
  const getCompletedWeeklyDateNights = useCallback(() => {
    return ideas.filter(idea => 
      idea.source === 'weekly' && idea.status === 'completed'
    );
  }, [ideas]);

  // Get all planned weekly Date Nights
  const getPlannedWeeklyDateNights = useCallback(() => {
    return ideas.filter(idea => 
      idea.source === 'weekly' && idea.status === 'planned'
    );
  }, [ideas]);

  // Sync status whenever ideas or week data changes
  useEffect(() => {
    syncWeeklyDateNightStatus();
  }, [syncWeeklyDateNightStatus]);

  return {
    isWeeklyDateNightCompleted,
    completeWeeklyDateNight,
    getWeeklyDateNightStatus,
    getCompletedWeeklyDateNights,
    getPlannedWeeklyDateNights,
    syncWeeklyDateNightStatus,
    weekDateNightMap
  };
};
