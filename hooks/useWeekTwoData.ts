import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../utils/logger';

export interface StrengthsBingoSquare {
  id: string;
  strength: string;
  isSelected: boolean;
  example: string;
  timestamp: number;
}

export interface DateNightPlan {
  when: string;
  where: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface FiveToOneRatio {
  positiveMoments: string[];
  timestamp: number;
}

export interface WeekTwoData {
  strengthsBingo: StrengthsBingoSquare[];
  dateNightPlan: DateNightPlan;
  chatPrompts: ChatPrompt[];
  fiveToOneRatio: FiveToOneRatio;
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_TWO_STORAGE_KEY = 'week_two_data';

const DEFAULT_STRENGTHS = [
  'Creative Genius',
  'Great Listener',
  'Empathy',
  'Champion Problem Solver',
  'Always Positive',
  'Supportive',
  'Hard Worker',
  'Adventurous',
  'Loyal',
  'Kind',
  'Patient',
  'Optimistic',
  'Choose Your Own',
  'Helpful',
  'Romantic',
  'Reliable',
  'Thoughtful',
  'Caring',
  'Quick Thinker',
  'Honest',
  'Independent',
  'Strong Communicator',
  'Level-Headed',
  'Motivated',
  'Funny'
];

export const useWeekTwoData = () => {
  const [data, setData] = useState<WeekTwoData>({
    strengthsBingo: DEFAULT_STRENGTHS.map((strength, index) => ({
      id: `strength-${index}`,
      strength,
      isSelected: false,
      example: '',
      timestamp: Date.now()
    })),
    dateNightPlan: { when: '', where: '', completed: false, timestamp: Date.now() },
    chatPrompts: [
      { 
        prompt: 'If you were a drag king/queen, what would be your name and best performance quality?', 
        playerOneAnswer: '', 
        playerTwoAnswer: '', 
        timestamp: Date.now() 
      },
      { 
        prompt: 'What was the most meaningful gift you\'ve ever received? Why was it so special?', 
        playerOneAnswer: '', 
        playerTwoAnswer: '', 
        timestamp: Date.now() 
      },
    ],
    fiveToOneRatio: {
      positiveMoments: ['', '', '', '', ''],
      timestamp: Date.now()
    },
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekTwoData>(WEEK_TWO_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Two data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekTwoData) => {
    try {
      await secureStorage.setItem(WEEK_TWO_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Two data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateStrengthsBingo = (index: number, updates: Partial<StrengthsBingoSquare>) => {
    const newData = { ...data };
    newData.strengthsBingo[index] = { ...newData.strengthsBingo[index], ...updates };
    saveData(newData);
  };

  const updateDateNightPlan = (updates: Partial<DateNightPlan>) => {
    const newData = { ...data };
    newData.dateNightPlan = { ...newData.dateNightPlan, ...updates };
    saveData(newData);
  };

  const updateChatPrompt = (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    saveData(newData);
  };

  const updateFiveToOneRatio = (updates: Partial<FiveToOneRatio>) => {
    const newData = { ...data };
    newData.fiveToOneRatio = { ...newData.fiveToOneRatio, ...updates };
    saveData(newData);
  };

  const updatePositiveMoment = (index: number, moment: string) => {
    const newData = { ...data };
    newData.fiveToOneRatio.positiveMoments[index] = moment;
    saveData(newData);
  };

  const updateCompletedSections = (sections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = sections;
    if (sections.every(Boolean)) {
      newData.completedAt = Date.now();
    }
    saveData(newData);
  };

  const resetData = () => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(WEEK_TWO_STORAGE_KEY);
      }
      setData({
        strengthsBingo: DEFAULT_STRENGTHS.map((strength, index) => ({
          id: `strength-${index}`,
          strength,
          isSelected: false,
          example: '',
          timestamp: Date.now()
        })),
        dateNightPlan: { when: '', where: '', completed: false, timestamp: Date.now() },
        chatPrompts: [
          { 
            prompt: 'If you were a drag king/queen, what would be your name and best performance quality?', 
            playerOneAnswer: '', 
            playerTwoAnswer: '', 
            timestamp: Date.now() 
          },
          { 
            prompt: 'What was the most meaningful gift you\'ve ever received? Why was it so special?', 
            playerOneAnswer: '', 
            playerTwoAnswer: '', 
            timestamp: Date.now() 
          },
        ],
        fiveToOneRatio: {
          positiveMoments: ['', '', '', '', ''],
          timestamp: Date.now()
        },
        completedSections: [false, false, false, false],
      });
    } catch (error) {
      console.error('Error resetting Week Two data:', error);
    }
  };

  return {
    data,
    isLoading,
    updateStrengthsBingo,
    updateDateNightPlan,
    updateChatPrompt,
    updateFiveToOneRatio,
    updatePositiveMoment,
    updateCompletedSections,
    resetData,
  };
};
