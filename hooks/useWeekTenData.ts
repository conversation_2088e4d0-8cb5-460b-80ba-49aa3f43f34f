import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../utils/logger';

export interface DreamBankGoal {
  item: string;
  amount: number;
  priority: number;
}

export interface GetArtsyDate {
  when: string;
  where: string;
  favoritePiece: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface MoneyTalk {
  moneyHistory: string;
  spendingHabits: string;
  guiltFreeJoys: string;
  sharedFinancialGoals: string;
  timestamp: number;
}

export interface WeekTenData {
  dreamBankGoals: DreamBankGoal[];
  getArtsyDate: GetArtsyDate;
  chatPrompts: ChatPrompt[];
  moneyTalk: MoneyTalk;
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_TEN_STORAGE_KEY = 'week_ten_data';

export const useWeekTenData = () => {
  const [data, setData] = useState<WeekTenData>({
    dreamBankGoals: [
      { item: 'Dream Home', amount: 0, priority: 1 },
      { item: 'Travel Fund', amount: 0, priority: 2 },
      { item: 'Emergency Fund', amount: 0, priority: 3 },
      { item: 'Investment Portfolio', amount: 0, priority: 4 },
      { item: 'Education Fund', amount: 0, priority: 5 },
      { item: 'Charitable Giving', amount: 0, priority: 6 },
      { item: 'Hobby Equipment', amount: 0, priority: 7 },
      { item: 'Vehicle Upgrade', amount: 0, priority: 8 },
      { item: 'Wedding/Honeymoon', amount: 0, priority: 9 },
      { item: 'Other Dreams', amount: 0, priority: 10 },
    ],
    getArtsyDate: {
      when: '',
      where: '',
      favoritePiece: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'What\'s a random skill you wish you could master overnight?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'If you could take a year off, what would you do?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    moneyTalk: {
      moneyHistory: '',
      spendingHabits: '',
      guiltFreeJoys: '',
      sharedFinancialGoals: '',
      timestamp: Date.now(),
    },
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekTenData>(WEEK_TEN_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Ten data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekTenData) => {
    try {
      await secureStorage.setItem(WEEK_TEN_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Ten data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateDreamBankGoal = async (index: number, updates: Partial<DreamBankGoal>) => {
    const newData = { ...data };
    newData.dreamBankGoals[index] = { ...newData.dreamBankGoals[index], ...updates };
    await saveData(newData);
  };

  const updateGetArtsyDate = async (updates: Partial<GetArtsyDate>) => {
    const newData = { ...data };
    newData.getArtsyDate = { ...newData.getArtsyDate, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateMoneyTalk = async (updates: Partial<MoneyTalk>) => {
    const newData = { ...data };
    newData.moneyTalk = { ...newData.moneyTalk, ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekTenData = {
      dreamBankGoals: [
        { item: 'Dream Home', amount: 0, priority: 1 },
        { item: 'Travel Fund', amount: 0, priority: 2 },
        { item: 'Emergency Fund', amount: 0, priority: 3 },
        { item: 'Investment Portfolio', amount: 0, priority: 4 },
        { item: 'Education Fund', amount: 0, priority: 5 },
        { item: 'Charitable Giving', amount: 0, priority: 6 },
        { item: 'Hobby Equipment', amount: 0, priority: 7 },
        { item: 'Vehicle Upgrade', amount: 0, priority: 8 },
        { item: 'Wedding/Honeymoon', amount: 0, priority: 9 },
        { item: 'Other Dreams', amount: 0, priority: 10 },
      ],
      getArtsyDate: {
        when: '',
        where: '',
        favoritePiece: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'What\'s a random skill you wish you could master overnight?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'If you could take a year off, what would you do?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      moneyTalk: {
        moneyHistory: '',
        spendingHabits: '',
        guiltFreeJoys: '',
        sharedFinancialGoals: '',
        timestamp: Date.now(),
      },
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateDreamBankGoal,
    updateGetArtsyDate,
    updateChatPrompt,
    updateMoneyTalk,
    updateCompletedSections,
    resetData,
  };
};
