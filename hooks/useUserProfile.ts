import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { validateName } from '../utils/validation';
import { logger } from '../utils/logger';

export interface UserProfile {
  partner1: {
    name: string;
    icon: string;
  };
  partner2: {
    name: string;
    icon: string;
  };
  isComplete: boolean;
  createdAt: number;
  updatedAt: number;
}

const USER_PROFILE_STORAGE_KEY = 'user_profile';

const DEFAULT_PROFILE: UserProfile = {
  partner1: {
    name: '',
    icon: '',
  },
  partner2: {
    name: '',
    icon: '',
  },
  isComplete: false,
  createdAt: Date.now(),
  updatedAt: Date.now(),
};

export const useUserProfile = () => {
  const [profile, setProfile] = useState<UserProfile>(DEFAULT_PROFILE);
  const [isLoading, setIsLoading] = useState(true);

  // Load profile from storage on mount
  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      logger.info('Loading profile from storage...');
      const storedProfile = await secureStorage.getItem<UserProfile>(USER_PROFILE_STORAGE_KEY);
      
      if (storedProfile) {
        // Validate the stored profile structure
        if (storedProfile.partner1 && storedProfile.partner2) {
          setProfile(storedProfile);
          logger.info('Profile loaded successfully');
        } else {
          logger.warn('Invalid profile structure found, using default');
          setProfile(DEFAULT_PROFILE);
        }
      } else {
        logger.info('No stored profile found, using default');
        setProfile(DEFAULT_PROFILE);
      }
    } catch (error) {
      logger.error('Error loading user profile:', error);
      // Set default profile on error to prevent app crashes
      setProfile(DEFAULT_PROFILE);
    } finally {
      setIsLoading(false);
    }
  };

  const saveProfile = async (newProfile: UserProfile) => {
    try {
      // Validate profile data before saving
      if (!newProfile.partner1 || !newProfile.partner2) {
        throw new Error('Invalid profile data: missing partner information');
      }

      const profileToSave = {
        ...newProfile,
        updatedAt: Date.now(),
      };
      
      await secureStorage.setItem(USER_PROFILE_STORAGE_KEY, profileToSave);
      setProfile(profileToSave);
      logger.info('Profile saved successfully');
    } catch (error) {
      logger.error('Error saving user profile:', error);
      throw new Error('Failed to save profile securely');
    }
  };

  const updatePartner1 = async (updates: Partial<UserProfile['partner1']>) => {
    try {
      // Create a copy of updates to avoid mutating the original object
      const processedUpdates = { ...updates };
      
      // Validate and sanitize name if it's provided
      if (processedUpdates.name !== undefined) {
        const nameValidation = validateName(processedUpdates.name);
        if (!nameValidation.isValid) {
          throw new Error(nameValidation.error || 'Invalid name');
        }
        processedUpdates.name = nameValidation.sanitizedValue!;
      }

      const newProfile = {
        ...profile,
        partner1: { ...profile.partner1, ...processedUpdates },
        isComplete: checkIfComplete({
          ...profile,
          partner1: { ...profile.partner1, ...processedUpdates },
        }),
      };
      
      await saveProfile(newProfile);
    } catch (error) {
      logger.error('Error updating partner 1:', error);
      throw error;
    }
  };

  const updatePartner2 = async (updates: Partial<UserProfile['partner2']>) => {
    try {
      // Create a copy of updates to avoid mutating the original object
      const processedUpdates = { ...updates };
      
      // Validate and sanitize name if it's provided
      if (processedUpdates.name !== undefined) {
        const nameValidation = validateName(processedUpdates.name);
        if (!nameValidation.isValid) {
          throw new Error(nameValidation.error || 'Invalid name');
        }
        processedUpdates.name = nameValidation.sanitizedValue!;
      }

      const newProfile = {
        ...profile,
        partner2: { ...profile.partner2, ...processedUpdates },
        isComplete: checkIfComplete({
          ...profile,
          partner2: { ...profile.partner2, ...processedUpdates },
        }),
      };
      
      await saveProfile(newProfile);
    } catch (error) {
      logger.error('Error updating partner 2:', error);
      throw error;
    }
  };

  const checkIfComplete = (profileToCheck: UserProfile): boolean => {
    return (
      profileToCheck.partner1.name.trim() !== '' &&
      profileToCheck.partner1.icon !== '' &&
      profileToCheck.partner2.name.trim() !== '' &&
      profileToCheck.partner2.icon !== ''
    );
  };

  const resetProfile = async () => {
    try {
      await secureStorage.removeItem(USER_PROFILE_STORAGE_KEY);
      setProfile(DEFAULT_PROFILE);
    } catch (error) {
      logger.error('Error resetting user profile:', error);
      throw new Error('Failed to reset profile securely');
    }
  };

  const getCoupleNames = (): string => {
    if (profile.isComplete) {
      return `${profile.partner1.name} & ${profile.partner2.name}`;
    }
    return 'Partner 1 & Partner 2';
  };

  const getPartnerNames = (): string[] => {
    if (profile.isComplete) {
      return [profile.partner1.name, profile.partner2.name];
    }
    return ['Partner 1', 'Partner 2'];
  };

  return {
    profile,
    isLoading,
    updatePartner1,
    updatePartner2,
    resetProfile,
    getCoupleNames,
    getPartnerNames,
    isComplete: profile.isComplete,
  };
};
