import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../utils/logger';

export interface BuildAStoryGame {
  storyStarters: string[];
  selectedStarter: string;
  playerOneContribution: string;
  playerTwoContribution: string;
  finalStory: string;
  timerUsed: boolean;
  timestamp: number;
}

export interface GetActiveDate {
  when: string;
  where: string;
  activity: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface SensateFocus {
  exercise: string;
  tried: boolean;
  reflections: string;
  timestamp: number;
}

export interface WeekTwelveData {
  buildAStoryGame: BuildAStoryGame;
  getActiveDate: GetActiveDate;
  chatPrompts: ChatPrompt[];
  sensateFocus: SensateFocus[];
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_TWELVE_STORAGE_KEY = 'week_twelve_data';

export const useWeekTwelveData = () => {
  const [data, setData] = useState<WeekTwelveData>({
    buildAStoryGame: {
      storyStarters: [
        'In a cozy hotel room...',
        'By candlelight...',
        'During a rainstorm...',
        'When a surprise arrived...',
        'At the spa...'
      ],
      selectedStarter: '',
      playerOneContribution: '',
      playerTwoContribution: '',
      finalStory: '',
      timerUsed: false,
      timestamp: Date.now(),
    },
    getActiveDate: {
      when: '',
      where: '',
      activity: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'I feel sexy when...', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'It feels good when you...', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    sensateFocus: [
      { exercise: 'Non-sexual touch', tried: false, reflections: '', timestamp: Date.now() },
      { exercise: 'Sensory exploration', tried: false, reflections: '', timestamp: Date.now() },
      { exercise: 'Guided breathing', tried: false, reflections: '', timestamp: Date.now() },
    ],
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekTwelveData>(WEEK_TWELVE_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Twelve data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekTwelveData) => {
    try {
      await secureStorage.setItem(WEEK_TWELVE_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Twelve data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateBuildAStoryGame = async (updates: Partial<BuildAStoryGame>) => {
    const newData = { ...data };
    newData.buildAStoryGame = { ...newData.buildAStoryGame, ...updates };
    await saveData(newData);
  };

  const updateGetActiveDate = async (updates: Partial<GetActiveDate>) => {
    const newData = { ...data };
    newData.getActiveDate = { ...newData.getActiveDate, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateSensateFocus = async (index: number, updates: Partial<SensateFocus>) => {
    const newData = { ...data };
    newData.sensateFocus[index] = { ...newData.sensateFocus[index], ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekTwelveData = {
      buildAStoryGame: {
        storyStarters: [
          'In a cozy hotel room...',
          'By candlelight...',
          'During a rainstorm...',
          'When a surprise arrived...',
          'At the spa...'
        ],
        selectedStarter: '',
        playerOneContribution: '',
        playerTwoContribution: '',
        finalStory: '',
        timerUsed: false,
        timestamp: Date.now(),
      },
      getActiveDate: {
        when: '',
        where: '',
        activity: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'I feel sexy when...', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'It feels good when you...', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      sensateFocus: [
        { exercise: 'Non-sexual touch', tried: false, reflections: '', timestamp: Date.now() },
        { exercise: 'Sensory exploration', tried: false, reflections: '', timestamp: Date.now() },
        { exercise: 'Guided breathing', tried: false, reflections: '', timestamp: Date.now() },
      ],
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateBuildAStoryGame,
    updateGetActiveDate,
    updateChatPrompt,
    updateSensateFocus,
    updateCompletedSections,
    resetData,
  };
};
