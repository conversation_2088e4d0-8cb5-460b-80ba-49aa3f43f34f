import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../utils/logger';

export interface PerfectSaturdayGame {
  playerOneAdjectives: string[];
  playerTwoAdjectives: string[];
  playerOneGuess: string[];
  playerTwoGuess: string[];
  finalAdjectives: string[];
  timestamp: number;
}

export interface BlendedPerfectDay {
  when: string;
  where: string;
  playerOneAdjective: string;
  playerTwoAdjective: string;
  blendedPlan: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface ConflictMapping {
  theMoment: string;
  deeperNeed: string;
  appreciation: string;
  timestamp: number;
}

export interface WeekEightData {
  perfectSaturdayGame: PerfectSaturdayGame;
  blendedPerfectDay: BlendedPerfectDay;
  chatPrompts: ChatPrompt[];
  conflictMapping: ConflictMapping[];
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_EIGHT_STORAGE_KEY = 'week_eight_data';

export const useWeekEightData = () => {
  const [data, setData] = useState<WeekEightData>({
    perfectSaturdayGame: {
      playerOneAdjectives: ['', '', '', '', ''],
      playerTwoAdjectives: ['', '', '', '', ''],
      playerOneGuess: ['', '', '', '', ''],
      playerTwoGuess: ['', '', '', '', ''],
      finalAdjectives: ['', '', '', '', ''],
      timestamp: Date.now(),
    },
    blendedPerfectDay: {
      when: '',
      where: '',
      playerOneAdjective: '',
      playerTwoAdjective: '',
      blendedPlan: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'If we could switch lives for a day, what would you do first?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'What\'s something I do that always makes you smile?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    conflictMapping: [
      { theMoment: '', deeperNeed: '', appreciation: '', timestamp: Date.now() },
      { theMoment: '', deeperNeed: '', appreciation: '', timestamp: Date.now() },
      { theMoment: '', deeperNeed: '', appreciation: '', timestamp: Date.now() },
    ],
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekEightData>(WEEK_EIGHT_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Eight data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekEightData) => {
    try {
      await secureStorage.setItem(WEEK_EIGHT_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Eight data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updatePerfectSaturdayGame = async (updates: Partial<PerfectSaturdayGame>) => {
    const newData = { ...data };
    newData.perfectSaturdayGame = { ...newData.perfectSaturdayGame, ...updates };
    await saveData(newData);
  };

  const updateBlendedPerfectDay = async (updates: Partial<BlendedPerfectDay>) => {
    const newData = { ...data };
    newData.blendedPerfectDay = { ...newData.blendedPerfectDay, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateConflictMapping = async (index: number, updates: Partial<ConflictMapping>) => {
    const newData = { ...data };
    newData.conflictMapping[index] = { ...newData.conflictMapping[index], ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekEightData = {
      perfectSaturdayGame: {
        playerOneAdjectives: ['', '', '', '', ''],
        playerTwoAdjectives: ['', '', '', '', ''],
        playerOneGuess: ['', '', '', '', ''],
        playerTwoGuess: ['', '', '', '', ''],
        finalAdjectives: ['', '', '', '', ''],
        timestamp: Date.now(),
      },
      blendedPerfectDay: {
        when: '',
        where: '',
        playerOneAdjective: '',
        playerTwoAdjective: '',
        blendedPlan: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'If we could switch lives for a day, what would you do first?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'What\'s something I do that always makes you smile?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      conflictMapping: [
        { theMoment: '', deeperNeed: '', appreciation: '', timestamp: Date.now() },
        { theMoment: '', deeperNeed: '', appreciation: '', timestamp: Date.now() },
        { theMoment: '', deeperNeed: '', appreciation: '', timestamp: Date.now() },
      ],
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updatePerfectSaturdayGame,
    updateBlendedPerfectDay,
    updateChatPrompt,
    updateConflictMapping,
    updateCompletedSections,
    resetData,
  };
};
