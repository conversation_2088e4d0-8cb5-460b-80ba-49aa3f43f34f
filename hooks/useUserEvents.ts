import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase/client';
import { useAuth } from '../contexts/AuthContext';
import { logger } from '../utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserEvent, getGuestEvents } from '../utils/userEventUtils';

export interface UseUserEventsReturn {
  logEvent: (eventName: string) => Promise<boolean>;
  hasEvent: (eventName: string) => boolean;
  getUserEvents: () => UserEvent[];
  isLoading: boolean;
  error: string | null;
}

// Common event names
export const USER_EVENTS = {
  ONBOARDING_STARTED: 'onboarding_started',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  FEATURE_X_CLICKED: 'feature_x_clicked',
  GUEST_MODE_STARTED: 'guest_mode_started',
  ACCOUNT_CREATED: 'account_created',
  SIGN_IN_SUCCESS: 'sign_in_success',
  SIGN_OUT: 'sign_out',
} as const;

export function useUserEvents(): UseUserEventsReturn {
  const { user, isAuthenticated } = useAuth();
  const [events, setEvents] = useState<UserEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user events when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserEvents();
    } else {
      setEvents([]);
    }
  }, [isAuthenticated, user]);

  const loadUserEvents = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('user_events')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        logger.error('Error loading user events:', fetchError);
        setError(fetchError.message);
        return;
      }

      setEvents(data || []);
    } catch (err) {
      logger.error('Error loading user events:', err);
      setError('Failed to load user events');
    } finally {
      setIsLoading(false);
    }
  };

  const logEvent = async (eventName: string): Promise<boolean> => {
    try {
      setError(null);

      if (isAuthenticated && user) {
        // Log to Supabase for authenticated users
        const { error: insertError } = await supabase
          .from('user_events')
          .insert({
            user_id: user.id,
            event_name: eventName,
          });

        if (insertError) {
          logger.error('Error logging event to Supabase:', insertError);
          setError(insertError.message);
          return false;
        }

        // Add to local state
        const newEvent: UserEvent = {
          id: `temp-${Date.now()}`,
          user_id: user.id,
          event_name: eventName,
          created_at: new Date().toISOString(),
        };
        setEvents(prev => [newEvent, ...prev]);

        logger.info('Event logged to Supabase:', { eventName, userId: user.id });
        return true;
      } else {
        // For guests, store locally in safe storage
        const guestEvents = await getGuestEvents();
        const newEvent = {
          id: `guest-${Date.now()}`,
          user_id: 'guest',
          event_name: eventName,
          created_at: new Date().toISOString(),
        };

        const updatedEvents = [newEvent, ...guestEvents];
        // Use safe storage through the utility function
        if (Platform.OS === 'web') {
          if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.setItem('guest_events', JSON.stringify(updatedEvents));
          }
        } else {
          await AsyncStorage.setItem('guest_events', JSON.stringify(updatedEvents));
        }

        logger.info('Event logged locally for guest:', { eventName });
        return true;
      }
    } catch (err) {
      logger.error('Error logging event:', err);
      setError('Failed to log event');
      return false;
    }
  };

  const hasEvent = (eventName: string): boolean => {
    if (isAuthenticated && user) {
      return events.some(event => event.event_name === eventName);
    } else {
      // For guests, check local storage
      return false; // We'll implement this with a separate function
    }
  };

  const getUserEvents = (): UserEvent[] => {
    return events;
  };

  return {
    logEvent,
    hasEvent,
    getUserEvents,
    isLoading,
    error,
  };
}

// Re-export helper functions for convenience
export { getGuestEvents, hasGuestEvent, migrateGuestEventsToUser, clearGuestEvents } from '../utils/userEventUtils';
