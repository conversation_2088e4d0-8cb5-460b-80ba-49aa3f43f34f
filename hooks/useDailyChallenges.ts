import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { logger } from '../utils/logger';
import { getDaysDifference, getStartOfToday, isSameDay } from '../utils/dateUtils';

export interface DailyChallenge {
  id: string;
  title: string;
  description: string;
  type: 'relationship' | 'communication' | 'fun' | 'growth';
  points: number;
  bonusPoints: number;
  completed: boolean;
  completedAt?: number;
  expiresAt: number;
  requirements: string[];
}

export interface ChallengeStreak {
  currentStreak: number;
  longestStreak: number;
  lastCompletedDate?: number;
  totalChallengesCompleted: number;
}

const DAILY_CHALLENGES_KEY = 'daily_challenges_data';
const CHALLENGE_STREAK_KEY = 'challenge_streak_data';

// Daily challenge templates
const CHALLENGE_TEMPLATES: Omit<DailyChallenge, 'id' | 'expiresAt' | 'completed'>[] = [
  {
    title: 'Love Note Surprise',
    description: 'Leave a sweet note for your partner in an unexpected place',
    type: 'relationship',
    points: 25,
    bonusPoints: 15,
    requirements: ['Write a heartfelt note', 'Hide it creatively', 'Partner finds it']
  },
  {
    title: 'Active Listening Master',
    description: 'Practice active listening for 10 minutes without interruption',
    type: 'communication',
    points: 30,
    bonusPoints: 20,
    requirements: ['Listen without speaking', 'Show understanding', 'Ask follow-up questions']
  },
  {
    title: 'Random Act of Kindness',
    description: 'Do something unexpectedly kind for your partner today',
    type: 'relationship',
    points: 20,
    bonusPoints: 10,
    requirements: ['Be spontaneous', 'Show thoughtfulness', 'Make them smile']
  },
  {
    title: 'Memory Lane Walk',
    description: 'Share a favorite memory from your relationship',
    type: 'fun',
    points: 25,
    bonusPoints: 15,
    requirements: ['Recall specific details', 'Share feelings', 'Listen to partner\'s memory']
  },
  {
    title: 'Growth Mindset Practice',
    description: 'Discuss one thing you\'d like to improve in your relationship',
    type: 'growth',
    points: 35,
    bonusPoints: 25,
    requirements: ['Be vulnerable', 'Listen without judgment', 'Make a plan together']
  },
  {
    title: 'Gratitude Circle',
    description: 'Express 3 things you\'re grateful for about your partner',
    type: 'relationship',
    points: 20,
    bonusPoints: 10,
    requirements: ['Be specific', 'Show appreciation', 'Listen to partner\'s gratitude']
  },
  {
    title: 'Fun Question Time',
    description: 'Ask your partner a deep, fun question you\'ve never asked',
    type: 'fun',
    points: 15,
    bonusPoints: 10,
    requirements: ['Be creative', 'Listen actively', 'Share your answer too']
  },
  {
    title: 'Conflict Resolution Practice',
    description: 'Practice using "I feel" statements in a calm discussion',
    type: 'communication',
    points: 40,
    bonusPoints: 30,
    requirements: ['Use "I feel" language', 'Stay calm', 'Find common ground']
  }
];

export const useDailyChallenges = () => {
  const [dailyChallenges, setDailyChallenges] = useState<DailyChallenge[]>([]);
  const [challengeStreak, setChallengeStreak] = useState<ChallengeStreak>({
    currentStreak: 0,
    longestStreak: 0,
    totalChallengesCompleted: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadDailyChallenges();
    loadChallengeStreak();
  }, []);

  const loadDailyChallenges = async () => {
    try {
      const storedChallenges = await secureStorage.getItem<DailyChallenge[]>(DAILY_CHALLENGES_KEY);
      if (storedChallenges) {
        setDailyChallenges(storedChallenges);
      } else {
        // Generate new daily challenges
        await generateDailyChallenges();
      }
    } catch (error) {
      logger.error('Error loading daily challenges:', error);
      await generateDailyChallenges();
    } finally {
      setIsLoading(false);
    }
  };

  const loadChallengeStreak = async () => {
    try {
      const storedStreak = await secureStorage.getItem<ChallengeStreak>(CHALLENGE_STREAK_KEY);
      if (storedStreak) {
        setChallengeStreak(storedStreak);
      }
    } catch (error) {
      logger.error('Error loading challenge streak:', error);
    }
  };

  const generateDailyChallenges = async () => {
    try {
      const now = Date.now();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      // Select 3 random challenges for today
      const shuffled = [...CHALLENGE_TEMPLATES].sort(() => 0.5 - Math.random());
      const selectedChallenges = shuffled.slice(0, 3).map((template, index) => ({
        ...template,
        id: `challenge-${Date.now()}-${index}`,
        expiresAt: tomorrow.getTime(),
        completed: false
      }));

      setDailyChallenges(selectedChallenges);
      await saveDailyChallenges(selectedChallenges);
    } catch (error) {
      logger.error('Error generating daily challenges:', error);
    }
  };

  const saveDailyChallenges = async (challenges: DailyChallenge[]) => {
    try {
      await secureStorage.setItem(DAILY_CHALLENGES_KEY, challenges);
    } catch (error) {
      logger.error('Error saving daily challenges:', error);
      throw new Error('Failed to save daily challenges');
    }
  };

  const saveChallengeStreak = async (streak: ChallengeStreak) => {
    try {
      await secureStorage.setItem(CHALLENGE_STREAK_KEY, streak);
    } catch (error) {
      logger.error('Error saving challenge streak:', error);
      throw new Error('Failed to save challenge streak');
    }
  };

  // Complete a daily challenge
  const completeChallenge = async (challengeId: string) => {
    try {
      const newChallenges = dailyChallenges.map(challenge => {
        if (challenge.id === challengeId && !challenge.completed) {
          return {
            ...challenge,
            completed: true,
            completedAt: Date.now()
          };
        }
        return challenge;
      });

      setDailyChallenges(newChallenges);
      await saveDailyChallenges(newChallenges);

      // Update streak
      await updateChallengeStreak();

      // Return the completed challenge for celebration
      const completedChallenge = newChallenges.find(c => c.id === challengeId);
      return completedChallenge;
    } catch (error) {
      logger.error('Error completing challenge:', error);
      throw error;
    }
  };

  // Update challenge streak
  const updateChallengeStreak = async () => {
    try {
      const now = Date.now();
      let newStreak = { ...challengeStreak };
      
      if (challengeStreak.lastCompletedDate) {
        const daysDiff = getDaysDifference(challengeStreak.lastCompletedDate, now);
        
        if (daysDiff === 1) {
          // Consecutive day
          newStreak.currentStreak += 1;
        } else if (daysDiff > 1) {
          // Streak broken - reset to 1
          newStreak.currentStreak = 1;
        }
        // daysDiff === 0 means same day, don't change streak
      } else {
        // First challenge completed
        newStreak.currentStreak = 1;
      }

      newStreak.lastCompletedDate = now;
      newStreak.totalChallengesCompleted += 1;
      
      if (newStreak.currentStreak > newStreak.longestStreak) {
        newStreak.longestStreak = newStreak.currentStreak;
      }

      setChallengeStreak(newStreak);
      await saveChallengeStreak(newStreak);
    } catch (error) {
      logger.error('Error updating challenge streak:', error);
    }
  };

  // Check if challenges need to be refreshed (new day)
  const checkAndRefreshChallenges = async () => {
    const now = Date.now();
    const hasExpiredChallenges = dailyChallenges.some(challenge => 
      challenge.expiresAt <= now && !challenge.completed
    );

    if (hasExpiredChallenges) {
      await generateDailyChallenges();
    }
  };

  // Get today's available challenges
  const getTodaysChallenges = () => {
    const now = Date.now();
    return dailyChallenges.filter(challenge => 
      challenge.expiresAt > now && !challenge.completed
    );
  };

  // Get completed challenges for today
  const getTodaysCompletedChallenges = () => {
    const now = Date.now();
    
    return dailyChallenges.filter(challenge => 
      challenge.completed && challenge.completedAt && 
      isSameDay(challenge.completedAt, now)
    );
  };

  // Get streak bonus points
  const getStreakBonus = () => {
    if (challengeStreak.currentStreak >= 7) return 50; // Week streak
    if (challengeStreak.currentStreak >= 3) return 25; // 3-day streak
    return 0;
  };

  // Reset all data (for testing)
  const resetDailyChallenges = async () => {
    try {
      await secureStorage.removeItem(DAILY_CHALLENGES_KEY);
      await secureStorage.removeItem(CHALLENGE_STREAK_KEY);
      setDailyChallenges([]);
      setChallengeStreak({
        currentStreak: 0,
        longestStreak: 0,
        totalChallengesCompleted: 0
      });
      await generateDailyChallenges();
    } catch (error) {
      logger.error('Error resetting daily challenges:', error);
    }
  };

  return {
    dailyChallenges,
    challengeStreak,
    isLoading,
    completeChallenge,
    getTodaysChallenges,
    getTodaysCompletedChallenges,
    checkAndRefreshChallenges,
    getStreakBonus,
    resetDailyChallenges
  };
};
