import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { logger } from '../utils/logger';

const ORIGIN_STORY_STORAGE_KEY = 'origin_story_data';

export interface StoryPhoto {
  id: string;
  uri: string;
  timestamp: number;
  caption?: string;
}

export interface OriginStoryData {
  firstMeeting: string;
  knewILovedYou: string;
  firstKiss: string;
  insideJokes: string;
  mostRomantic: string;
  biggestChallenge: string;
  bestMemories: string;
  firstMeetingPhotos: StoryPhoto[];
  knewILovedYouPhotos: StoryPhoto[];
  firstKissPhotos: StoryPhoto[];
  insideJokesPhotos: StoryPhoto[];
  mostRomanticPhotos: StoryPhoto[];
  biggestChallengePhotos: StoryPhoto[];
  bestMemoriesPhotos: StoryPhoto[];
  completedSections: string[];
  lastUpdated: number;
}

const DEFAULT_DATA: OriginStoryData = {
  firstMeeting: '',
  knewILovedYou: '',
  firstKiss: '',
  insideJokes: '',
  mostRomantic: '',
  biggestChallenge: '',
  bestMemories: '',
  firstMeetingPhotos: [],
  knewILovedYouPhotos: [],
  firstKissPhotos: [],
  insideJokesPhotos: [],
  mostRomanticPhotos: [],
  biggestChallengePhotos: [],
  bestMemoriesPhotos: [],
  completedSections: [],
  lastUpdated: Date.now(),
};

export function useOriginStoryData() {
  const [data, setData] = useState<OriginStoryData>(DEFAULT_DATA);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const storedData = await secureStorage.getItem(ORIGIN_STORY_STORAGE_KEY);
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setData({ ...DEFAULT_DATA, ...parsedData });
      }
    } catch (error) {
      logger.error('Error loading origin story data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateField = async (field: keyof OriginStoryData, value: string) => {
    try {
      const newData = {
        ...data,
        [field]: value,
        lastUpdated: Date.now(),
      };
      
      // Set completion date when a field is filled for the first time
      const dateField = `${field}Date` as keyof OriginStoryData;
      if (value.trim() !== '' && !data[dateField]) {
        (newData as any)[dateField] = Date.now();
      }
      
      setData(newData);
      await saveData(newData);
    } catch (error) {
      logger.error('Error updating origin story field:', error);
      throw error;
    }
  };

  const addPhoto = async (section: keyof OriginStoryData, photo: StoryPhoto) => {
    try {
      const photoArray = data[section] as StoryPhoto[];
      if (Array.isArray(photoArray)) {
        const newPhotos = [...photoArray, photo];
        const newData = {
          ...data,
          [section]: newPhotos,
          lastUpdated: Date.now(),
        };
        setData(newData);
        await saveData(newData);
      }
    } catch (error) {
      logger.error('Error adding photo to origin story:', error);
      throw error;
    }
  };

  const removePhoto = async (section: keyof OriginStoryData, photoId: string) => {
    try {
      const photoArray = data[section] as StoryPhoto[];
      if (Array.isArray(photoArray)) {
        const newPhotos = photoArray.filter(photo => photo.id !== photoId);
        const newData = {
          ...data,
          [section]: newPhotos,
          lastUpdated: Date.now(),
        };
        setData(newData);
        await saveData(newData);
      }
    } catch (error) {
      logger.error('Error removing photo from origin story:', error);
      throw error;
    }
  };

  const updateCompletedSections = async (sections: string[]) => {
    try {
      const newData = {
        ...data,
        completedSections: sections,
        lastUpdated: Date.now(),
      };
      setData(newData);
      await saveData(newData);
    } catch (error) {
      logger.error('Error updating completed sections:', error);
      throw error;
    }
  };

  const saveData = async (dataToSave: OriginStoryData) => {
    try {
      await secureStorage.setItem(ORIGIN_STORY_STORAGE_KEY, JSON.stringify(dataToSave));
      logger.info('Origin story data saved successfully');
    } catch (error) {
      logger.error('Error saving origin story data:', error);
      throw error;
    }
  };

  const resetData = async () => {
    try {
      await secureStorage.removeItem(ORIGIN_STORY_STORAGE_KEY);
      setData(DEFAULT_DATA);
      logger.info('Origin story data reset successfully');
    } catch (error) {
      logger.error('Error resetting origin story data:', error);
      throw error;
    }
  };

  const getCompletionPercentage = (): number => {
    const totalFields = 7; // firstMeeting, knewILovedYou, firstKiss, insideJokes, mostRomantic, biggestChallenge, bestMemories
    const completedFields = Object.entries(data).filter(([key, value]) => {
      if (key === 'completedSections' || key === 'lastUpdated' || key.includes('Photos')) return false;
      return value && typeof value === 'string' && value.trim() !== '';
    }).length;
    
    return Math.round((completedFields / totalFields) * 100);
  };

  const isComplete = (): boolean => {
    return getCompletionPercentage() === 100;
  };

  return {
    data,
    isLoading,
    updateField,
    addPhoto,
    removePhoto,
    updateCompletedSections,
    saveData,
    resetData,
    getCompletionPercentage,
    isComplete,
  };
}
