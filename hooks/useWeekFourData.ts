import { useState, useEffect } from 'react';

export interface PlaylistSong {
  id: string;
  title: string;
  artist: string;
  note: string;
  timestamp: number;
}

export interface MovieThemeNight {
  when: string;
  where: string;
  theme: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface EmotionalRegulation {
  pauseStatement: string;
  iStatement: string;
  connectInvitation: string;
  timestamp: number;
}

export interface WeekFourData {
  playlistSongs: PlaylistSong[];
  movieThemeNight: MovieThemeNight;
  chatPrompts: ChatPrompt[];
  emotionalRegulation: EmotionalRegulation;
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_FOUR_STORAGE_KEY = 'week_four_data';

const INSPIRATION_PROMPTS = [
  'A song that reminds you of your partner',
  'A song that captures a challenge you\'ve faced',
  'A song that helps you feel close or supported',
  'A song that makes you laugh or want to dance',
  'A song that brings back a great memory',
  'A song for your next adventure',
  'A song you\'d choose as your anthem'
];

const DEFAULT_CHAT_PROMPTS = [
  'What excites you about life right now?',
  'What lesson took you the longest to learn?'
];

export const useWeekFourData = () => {
  const [data, setData] = useState<WeekFourData>({
    playlistSongs: [],
    movieThemeNight: { 
      when: '', 
      where: '', 
      theme: '', 
      completed: false, 
      timestamp: Date.now() 
    },
    chatPrompts: DEFAULT_CHAT_PROMPTS.map((prompt, index) => ({
      prompt,
      playerOneAnswer: '',
      playerTwoAnswer: '',
      timestamp: Date.now()
    })),
    emotionalRegulation: {
      pauseStatement: '',
      iStatement: '',
      connectInvitation: '',
      timestamp: Date.now()
    },
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const stored = localStorage.getItem(WEEK_FOUR_STORAGE_KEY);
      if (stored) {
        setData(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading week four data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekFourData) => {
    try {
      setData(newData);
      localStorage.setItem(WEEK_FOUR_STORAGE_KEY, JSON.stringify(newData));
    } catch (error) {
      console.error('Error saving week four data:', error);
    }
  };

  const addPlaylistSong = (song: Omit<PlaylistSong, 'id' | 'timestamp'>) => {
    const newSong: PlaylistSong = {
      ...song,
      id: `song-${Date.now()}-${Math.random()}`,
      timestamp: Date.now()
    };
    const newData = {
      ...data,
      playlistSongs: [...data.playlistSongs, newSong]
    };
    saveData(newData);
  };

  const updatePlaylistSong = (songId: string, updates: Partial<PlaylistSong>) => {
    const newData = {
      ...data,
      playlistSongs: data.playlistSongs.map(song => 
        song.id === songId ? { ...song, ...updates } : song
      )
    };
    saveData(newData);
  };

  const removePlaylistSong = (songId: string) => {
    const newData = {
      ...data,
      playlistSongs: data.playlistSongs.filter(song => song.id !== songId)
    };
    saveData(newData);
  };

  const updateMovieThemeNight = (updates: Partial<MovieThemeNight>) => {
    const newData = {
      ...data,
      movieThemeNight: { ...data.movieThemeNight, ...updates }
    };
    saveData(newData);
  };

  const updateChatPrompt = (promptIndex: number, updates: Partial<ChatPrompt>) => {
    const newData = {
      ...data,
      chatPrompts: data.chatPrompts.map((p, index) => 
        index === promptIndex ? { ...p, ...updates } : p
      )
    };
    saveData(newData);
  };

  const updateEmotionalRegulation = (updates: Partial<EmotionalRegulation>) => {
    const newData = {
      ...data,
      emotionalRegulation: { ...data.emotionalRegulation, ...updates }
    };
    saveData(newData);
  };

  const updateCompletedSections = (sections: boolean[]) => {
    const newData = {
      ...data,
      completedSections: sections,
      completedAt: sections.every(s => s) ? Date.now() : undefined
    };
    saveData(newData);
  };

  const getInspirationPrompts = () => INSPIRATION_PROMPTS;

  return {
    data,
    isLoading,
    addPlaylistSong,
    updatePlaylistSong,
    removePlaylistSong,
    updateMovieThemeNight,
    updateChatPrompt,
    updateEmotionalRegulation,
    updateCompletedSections,
    getInspirationPrompts
  };
};
