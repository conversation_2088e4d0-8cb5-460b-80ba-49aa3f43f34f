import { supabase } from '../lib/supabase/client';
import { logger } from '../utils/logger';

export interface DateNightIdea {
  id: string;
  title: string;
  description: string;
  source: 'weekly' | 'user' | 'system';
  weekNumber?: number;
  emoji?: string;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  estimatedDuration?: number; // in minutes
  costLevel?: 'free' | 'low' | 'medium' | 'high';
  indoorOutdoor?: 'indoor' | 'outdoor' | 'both';
}

export interface UserDateNightIdea {
  id: string;
  userId: string;
  ideaId: string;
  status: 'planned' | 'completed' | 'favorite';
  completedAt?: string;
  notes?: string;
  rating?: number; // 1-5 stars
  createdAt: string;
  updatedAt: string;
}

class DateNightIdeasService {
  /**
   * Get all available date night ideas
   */
  async getAllIdeas(): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .order('title');

      if (error) {
        logger.error('Error fetching date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching date night ideas:', error);
      return [];
    }
  }

  /**
   * Get random date night ideas
   */
  async getRandomIdeas(count: number = 5): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_random_date_night_ideas', { count });

      if (error) {
        logger.error('Error fetching random date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching random date night ideas:', error);
      return [];
    }
  }

  /**
   * Get date night ideas by category
   */
  async getIdeasByCategory(category: string): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_date_night_ideas_by_category', { category_filter: category });

      if (error) {
        logger.error('Error fetching date night ideas by category:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching date night ideas by category:', error);
      return [];
    }
  }

  /**
   * Get date night ideas by difficulty
   */
  async getIdeasByDifficulty(difficulty: 'easy' | 'medium' | 'hard'): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_date_night_ideas_by_difficulty', { difficulty_filter: difficulty });

      if (error) {
        logger.error('Error fetching date night ideas by difficulty:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching date night ideas by difficulty:', error);
      return [];
    }
  }

  /**
   * Get weekly date night ideas
   */
  async getWeeklyIdeas(): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .eq('source', 'weekly')
        .order('week_number');

      if (error) {
        logger.error('Error fetching weekly date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching weekly date night ideas:', error);
      return [];
    }
  }

  /**
   * Get a specific date night idea by ID
   */
  async getIdeaById(id: string): Promise<DateNightIdea | null> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        logger.error('Error fetching date night idea by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error fetching date night idea by ID:', error);
      return null;
    }
  }

  /**
   * Get user's saved date night ideas
   */
  async getUserIdeas(userId: string): Promise<UserDateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('user_date_night_ideas')
        .select(`
          *,
          date_night_ideas_global (
            id,
            title,
            description,
            emoji,
            category,
            difficulty,
            estimated_duration,
            cost_level,
            indoor_outdoor
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error fetching user date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error fetching user date night ideas:', error);
      return [];
    }
  }

  /**
   * Save a date night idea to user's collection
   */
  async saveUserIdea(userId: string, ideaId: string, status: 'planned' | 'completed' | 'favorite', notes?: string, rating?: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_date_night_ideas')
        .upsert({
          user_id: userId,
          idea_id: ideaId,
          status,
          notes,
          rating,
          completed_at: status === 'completed' ? new Date().toISOString() : null,
        }, {
          onConflict: 'user_id,idea_id'
        });

      if (error) {
        logger.error('Error saving user date night idea:', error);
        return false;
      }

      logger.info('User date night idea saved successfully');
      return true;
    } catch (error) {
      logger.error('Error saving user date night idea:', error);
      return false;
    }
  }

  /**
   * Remove a date night idea from user's collection
   */
  async removeUserIdea(userId: string, ideaId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_date_night_ideas')
        .delete()
        .eq('user_id', userId)
        .eq('idea_id', ideaId);

      if (error) {
        logger.error('Error removing user date night idea:', error);
        return false;
      }

      logger.info('User date night idea removed successfully');
      return true;
    } catch (error) {
      logger.error('Error removing user date night idea:', error);
      return false;
    }
  }

  /**
   * Get categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        logger.error('Error fetching categories:', error);
        return [];
      }

      const categories = [...new Set(data?.map(item => item.category).filter(Boolean))];
      return categories.sort();
    } catch (error) {
      logger.error('Error fetching categories:', error);
      return [];
    }
  }

  /**
   * Search date night ideas
   */
  async searchIdeas(query: string): Promise<DateNightIdea[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .or(`title.ilike.%${query}%,description.ilike.%${query}%,category.ilike.%${query}%`)
        .order('title');

      if (error) {
        logger.error('Error searching date night ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error searching date night ideas:', error);
      return [];
    }
  }
}

export const dateNightIdeasService = new DateNightIdeasService();
export default dateNightIdeasService;
