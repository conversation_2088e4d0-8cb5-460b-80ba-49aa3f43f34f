import { supabase } from '../lib/supabase/client';
import { logger } from '../utils/logger';

export interface MealIdea {
  id: string;
  title: string;
  description?: string;
  category: string;
  emoji?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  prepTime?: number; // in minutes
  cookTime?: number; // in minutes
  servings: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  source: 'global' | 'user';
  weekNumber?: number;
  isFavorite?: boolean;
  isCompleted?: boolean;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface GlobalMealIdea {
  id: string;
  title: string;
  description?: string;
  category: string;
  emoji?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  prepTime?: number;
  cookTime?: number;
  servings: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  source: string;
  weekNumber?: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserMealIdea {
  id: string;
  userId: string;
  ideaId?: string; // reference to global idea
  title: string;
  description?: string;
  category: string;
  emoji?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  prepTime?: number;
  cookTime?: number;
  servings: number;
  ingredients: string[];
  instructions: string[];
  tags: string[];
  source: string;
  weekNumber?: number;
  isFavorite: boolean;
  isCompleted: boolean;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

class MealIdeasService {
  /**
   * Get all global meal ideas
   */
  async getGlobalMealIdeas(category?: string, weekNumber?: number): Promise<GlobalMealIdea[]> {
    try {
      let query = supabase
        .from('meal_ideas_global')
        .select('*')
        .order('created_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      if (weekNumber) {
        query = query.eq('week_number', weekNumber);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('Error fetching global meal ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getGlobalMealIdeas:', error);
      return [];
    }
  }

  /**
   * Get user's meal ideas
   */
  async getUserMealIdeas(userId: string, category?: string): Promise<UserMealIdea[]> {
    try {
      let query = supabase
        .from('meal_ideas_users')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('Error fetching user meal ideas:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getUserMealIdeas:', error);
      return [];
    }
  }

  /**
   * Get combined meal ideas (global + user's)
   */
  async getCombinedMealIdeas(userId: string, category?: string): Promise<MealIdea[]> {
    try {
      const [globalIdeas, userIdeas] = await Promise.all([
        this.getGlobalMealIdeas(category),
        this.getUserMealIdeas(userId, category)
      ]);

      // Convert global ideas to MealIdea format
      const globalMealIdeas: MealIdea[] = (globalIdeas || []).map(idea => ({
        id: idea.id,
        title: idea.title,
        description: idea.description,
        category: idea.category,
        emoji: idea.emoji,
        difficulty: idea.difficulty,
        prepTime: idea.prepTime,
        cookTime: idea.cookTime,
        servings: idea.servings,
        ingredients: idea.ingredients,
        instructions: idea.instructions,
        tags: idea.tags,
        source: 'global' as const,
        weekNumber: idea.weekNumber,
        createdAt: idea.createdAt,
        updatedAt: idea.updatedAt
      }));

      // Convert user ideas to MealIdea format
      const userMealIdeas: MealIdea[] = (userIdeas || []).map(idea => ({
        id: idea.id,
        title: idea.title,
        description: idea.description,
        category: idea.category,
        emoji: idea.emoji,
        difficulty: idea.difficulty,
        prepTime: idea.prepTime,
        cookTime: idea.cookTime,
        servings: idea.servings,
        ingredients: idea.ingredients,
        instructions: idea.instructions,
        tags: idea.tags,
        source: 'user' as const,
        weekNumber: idea.weekNumber,
        isFavorite: idea.isFavorite,
        isCompleted: idea.isCompleted,
        completedAt: idea.completedAt,
        createdAt: idea.createdAt,
        updatedAt: idea.updatedAt
      }));

      // Combine and sort by creation date
      return [...userMealIdeas, ...globalMealIdeas].sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } catch (error) {
      logger.error('Error in getCombinedMealIdeas:', error);
      return [];
    }
  }

  /**
   * Create a new user meal idea
   */
  async createUserMealIdea(userId: string, mealIdea: Omit<UserMealIdea, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<UserMealIdea | null> {
    try {
      const { data, error } = await supabase
        .from('meal_ideas_users')
        .insert({
          user_id: userId,
          title: mealIdea.title,
          description: mealIdea.description,
          category: mealIdea.category,
          emoji: mealIdea.emoji,
          difficulty: mealIdea.difficulty,
          prep_time: mealIdea.prepTime,
          cook_time: mealIdea.cookTime,
          servings: mealIdea.servings,
          ingredients: mealIdea.ingredients,
          instructions: mealIdea.instructions,
          tags: mealIdea.tags,
          source: mealIdea.source,
          week_number: mealIdea.weekNumber,
          is_favorite: mealIdea.isFavorite,
          is_completed: mealIdea.isCompleted,
          completed_at: mealIdea.completedAt
        })
        .select()
        .single();

      if (error) {
        logger.error('Error creating user meal idea:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error in createUserMealIdea:', error);
      return null;
    }
  }

  /**
   * Update a user meal idea
   */
  async updateUserMealIdea(userId: string, ideaId: string, updates: Partial<UserMealIdea>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meal_ideas_users')
        .update({
          title: updates.title,
          description: updates.description,
          category: updates.category,
          emoji: updates.emoji,
          difficulty: updates.difficulty,
          prep_time: updates.prepTime,
          cook_time: updates.cookTime,
          servings: updates.servings,
          ingredients: updates.ingredients,
          instructions: updates.instructions,
          tags: updates.tags,
          source: updates.source,
          week_number: updates.weekNumber,
          is_favorite: updates.isFavorite,
          is_completed: updates.isCompleted,
          completed_at: updates.completedAt
        })
        .eq('id', ideaId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error updating user meal idea:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in updateUserMealIdea:', error);
      return false;
    }
  }

  /**
   * Delete a user meal idea
   */
  async deleteUserMealIdea(userId: string, ideaId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meal_ideas_users')
        .delete()
        .eq('id', ideaId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error deleting user meal idea:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in deleteUserMealIdea:', error);
      return false;
    }
  }


  /**
   * Mark a meal idea as completed
   */
  async markAsCompleted(userId: string, ideaId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meal_ideas_users')
        .update({ 
          is_completed: true,
          completed_at: new Date().toISOString()
        })
        .eq('id', ideaId)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error marking meal as completed:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in markAsCompleted:', error);
      return false;
    }
  }

  /**
   * Get meal categories
   */
  async getMealCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('meal_ideas_global')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        logger.error('Error fetching meal categories:', error);
        return [];
      }

      // Get unique categories
      const categories = [...new Set((data || []).map(item => item.category))];
      return categories.sort();
    } catch (error) {
      logger.error('Error in getMealCategories:', error);
      return [];
    }
  }

  /**
   * Search meal ideas
   */
  async searchMealIdeas(userId: string, searchTerm: string, category?: string): Promise<MealIdea[]> {
    try {
      const allIdeas = await this.getCombinedMealIdeas(userId, category);
      
      const searchLower = searchTerm.toLowerCase();
      return allIdeas.filter(idea => 
        idea.title.toLowerCase().includes(searchLower) ||
        idea.description?.toLowerCase().includes(searchLower) ||
        idea.ingredients.some(ingredient => ingredient.toLowerCase().includes(searchLower)) ||
        idea.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    } catch (error) {
      logger.error('Error in searchMealIdeas:', error);
      return [];
    }
  }
}

export const mealIdeasService = new MealIdeasService();
