import { supabase } from '../lib/supabase/client';
import { logger } from '../utils/logger';

export interface Achievement {
  id: string;
  title: string;
  description: string;
  points: number;
  type: 'activity' | 'module' | 'streak' | 'milestone';
  icon?: string;
  unlockedAt: string;
}

export interface PointsSystemData {
  id: string;
  userId: string;
  totalPoints: number;
  level: number;
  achievements: Achievement[];
  lastActivity: string | null;
  createdAt: string;
  updatedAt: string;
}

class PointsSystemService {
  /**
   * Get user's points system data
   */
  async getUserPoints(userId: string): Promise<PointsSystemData | null> {
    try {
      const { data, error } = await supabase
        .from('points_system')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No data found, create new record
          return await this.createUserPoints(userId);
        }
        logger.error('Error fetching user points:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error in getUserPoints:', error);
      return null;
    }
  }

  /**
   * Create new points system record for user
   */
  async createUserPoints(userId: string): Promise<PointsSystemData | null> {
    try {
      const { data, error } = await supabase
        .from('points_system')
        .insert({
          user_id: userId,
          total_points: 0,
          level: 1,
          achievements: [],
          last_activity: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        logger.error('Error creating user points:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error in createUserPoints:', error);
      return null;
    }
  }

  /**
   * Add points to user's total
   */
  async addPoints(userId: string, points: number, activityType?: string): Promise<boolean> {
    try {
      // Get current data
      const currentData = await this.getUserPoints(userId);
      if (!currentData) return false;

      const newTotalPoints = currentData.totalPoints + points;
      const newLevel = this.calculateLevel(newTotalPoints);

      const { error } = await supabase
        .from('points_system')
        .update({
          total_points: newTotalPoints,
          level: newLevel,
          last_activity: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        logger.error('Error adding points:', error);
        return false;
      }

      // Check for level up achievements
      if (newLevel > currentData.level) {
        await this.addAchievement(userId, {
          id: `level_${newLevel}`,
          title: `Level ${newLevel} Reached!`,
          description: `You've reached level ${newLevel}! Keep up the great work!`,
          points: 0,
          type: 'milestone',
          icon: '🏆',
          unlockedAt: new Date().toISOString()
        });
      }

      return true;
    } catch (error) {
      logger.error('Error in addPoints:', error);
      return false;
    }
  }

  /**
   * Add achievement to user's collection
   */
  async addAchievement(userId: string, achievement: Achievement): Promise<boolean> {
    try {
      const currentData = await this.getUserPoints(userId);
      if (!currentData) return false;

      // Check if achievement already exists
      const existingAchievement = currentData.achievements.find(a => a.id === achievement.id);
      if (existingAchievement) return true; // Already unlocked

      const updatedAchievements = [...currentData.achievements, achievement];

      const { error } = await supabase
        .from('points_system')
        .update({
          achievements: updatedAchievements,
          last_activity: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        logger.error('Error adding achievement:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in addAchievement:', error);
      return false;
    }
  }

  /**
   * Get user's achievements
   */
  async getUserAchievements(userId: string): Promise<Achievement[]> {
    try {
      const data = await this.getUserPoints(userId);
      return data?.achievements || [];
    } catch (error) {
      logger.error('Error in getUserAchievements:', error);
      return [];
    }
  }

  /**
   * Get user's level and points info
   */
  async getUserLevel(userId: string): Promise<{ level: number; totalPoints: number; pointsToNextLevel: number } | null> {
    try {
      const data = await this.getUserPoints(userId);
      if (!data) return null;

      const pointsToNextLevel = this.getPointsToNextLevel(data.totalPoints, data.level);

      return {
        level: data.level,
        totalPoints: data.totalPoints,
        pointsToNextLevel
      };
    } catch (error) {
      logger.error('Error in getUserLevel:', error);
      return null;
    }
  }

  /**
   * Calculate level based on total points
   */
  private calculateLevel(totalPoints: number): number {
    // Level formula: every 100 points = 1 level
    return Math.floor(totalPoints / 100) + 1;
  }

  /**
   * Get points needed to reach next level
   */
  private getPointsToNextLevel(totalPoints: number, currentLevel: number): number {
    const nextLevelPoints = currentLevel * 100;
    return Math.max(0, nextLevelPoints - totalPoints);
  }

  /**
   * Get leaderboard data (top users by points)
   */
  async getLeaderboard(limit: number = 10): Promise<Array<{ userId: string; totalPoints: number; level: number }>> {
    try {
      const { data, error } = await supabase
        .from('points_system')
        .select('user_id, total_points, level')
        .order('total_points', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching leaderboard:', error);
        return [];
      }

      return (data || []).map(item => ({
        userId: item.user_id,
        totalPoints: item.total_points,
        level: item.level
      }));
    } catch (error) {
      logger.error('Error in getLeaderboard:', error);
      return [];
    }
  }

  /**
   * Reset user's points (for testing/admin purposes)
   */
  async resetUserPoints(userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('points_system')
        .update({
          total_points: 0,
          level: 1,
          achievements: [],
          last_activity: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        logger.error('Error resetting user points:', error);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error in resetUserPoints:', error);
      return false;
    }
  }
}

export const pointsSystemService = new PointsSystemService();
