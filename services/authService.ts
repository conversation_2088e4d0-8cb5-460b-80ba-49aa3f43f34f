import { supabase, getCurrentUser } from '../lib/supabase/client';
import { logger } from '../utils/logger';
import { UserProfile } from '../hooks/useUserProfile';

export interface AuthUser {
  id: string;
  email?: string;
  created_at: string;
  updated_at: string;
}

export interface SignUpData {
  email: string;
  password: string;
  partner1Name: string;
  partner1Icon: string;
  partner2Name: string;
  partner2Icon: string;
}

export interface SignInData {
  email: string;
  password: string;
}

class AuthService {
  /**
   * Sign up a new user
   */
  async signUp(data: SignUpData) {
    try {
      logger.info('Attempting to sign up user:', { email: data.email });
      
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
      });

      if (authError) {
        logger.error('Auth signup error:', authError);
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error('Failed to create user account');
      }

      // Create user profile
      const userProfile: UserProfile = {
        partner1: {
          name: data.partner1Name,
          icon: data.partner1Icon,
        },
        partner2: {
          name: data.partner2Name,
          icon: data.partner2Icon,
        },
        isComplete: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      console.log('🔍 Creating profile for user:', authData.user.id);
      
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          partner1_name: data.partner1Name,
          partner1_icon: data.partner1Icon,
          partner2_name: data.partner2Name,
          partner2_icon: data.partner2Icon,
          is_complete: true,
        })
        .select();

      if (profileError) {
        console.error('❌ Profile creation error:', profileError);
        logger.error('Profile creation error:', profileError);
        throw new Error(`Failed to create user profile: ${profileError.message}`);
      }

      console.log('✅ Profile created successfully:', profileData);

      logger.info('User signed up successfully:', { userId: authData.user.id });
      return { user: authData.user, profile: userProfile };
    } catch (error) {
      logger.error('Sign up error:', error);
      throw error;
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn(data: SignInData) {
    try {
      logger.info('Attempting to sign in user:', { email: data.email });
      
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (authError) {
        logger.error('Auth signin error:', authError);
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error('Failed to sign in');
      }

      // Get user profile
      const profile = await this.getUserProfile(authData.user.id);
      
      logger.info('User signed in successfully:', { userId: authData.user.id });
      return { user: authData.user, profile };
    } catch (error) {
      logger.error('Sign in error:', error);
      throw error;
    }
  }

  /**
   * Sign out the current user
   */
  async signOut() {
    try {
      logger.info('Signing out user');
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        logger.error('Sign out error:', error);
        throw error;
      }
      
      logger.info('User signed out successfully');
    } catch (error) {
      logger.error('Sign out error:', error);
      throw error;
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser() {
    try {
      const user = await getCurrentUser();
      if (!user) {
        return null;
      }

      const profile = await this.getUserProfile(user.id);
      return { user, profile };
    } catch (error) {
      logger.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Get user profile from database
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('Get profile error:', error);
        return null;
      }

      if (!data) {
        return null;
      }

      return {
        partner1: {
          name: data.partner1_name,
          icon: data.partner1_icon,
        },
        partner2: {
          name: data.partner2_name,
          icon: data.partner2_icon,
        },
        isComplete: data.is_complete,
        createdAt: new Date(data.created_at).getTime(),
        updatedAt: new Date(data.updated_at).getTime(),
      };
    } catch (error) {
      logger.error('Get user profile error:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, profile: Partial<UserProfile>) {
    try {
      logger.info('Updating user profile:', { userId });
      
      const updateData: any = {};
      
      if (profile.partner1) {
        updateData.partner1_name = profile.partner1.name;
        updateData.partner1_icon = profile.partner1.icon;
      }
      
      if (profile.partner2) {
        updateData.partner2_name = profile.partner2.name;
        updateData.partner2_icon = profile.partner2.icon;
      }
      
      if (profile.isComplete !== undefined) {
        updateData.is_complete = profile.isComplete;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        logger.error('Update profile error:', error);
        throw new Error('Failed to update profile');
      }

      logger.info('Profile updated successfully');
    } catch (error) {
      logger.error('Update user profile error:', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated() {
    try {
      const user = await getCurrentUser();
      return !!user;
    } catch (error) {
      logger.error('Check authentication error:', error);
      return false;
    }
  }

  /**
   * Reset password
   */
  async resetPassword(email: string) {
    try {
      logger.info('Resetting password for:', { email });
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'everlasting-us://reset-password',
      });

      if (error) {
        logger.error('Reset password error:', error);
        throw new Error(error.message);
      }

      logger.info('Password reset email sent');
    } catch (error) {
      logger.error('Reset password error:', error);
      throw error;
    }
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}

export const authService = new AuthService();
export default authService;
