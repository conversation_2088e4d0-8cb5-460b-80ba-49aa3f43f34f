import { logger } from '../utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ErrorReport {
  id: string;
  timestamp: string;
  error: string;
  stack?: string;
  sessionId?: string; // Anonymous session identifier instead of userId
  platform?: string; // Generic platform info instead of userAgent
  component?: string;
  action?: string;
  metadata?: Record<string, any>; // Will be sanitized to remove PII
}

export interface ErrorReportingConsent {
  hasConsented: boolean;
  consentTimestamp?: string;
  consentVersion: string;
}

class ErrorReportingService {
  private errorQueue: ErrorReport[] = [];
  private readonly MAX_QUEUE_SIZE = 50;
  private readonly STORAGE_KEY = 'error_reports_queue';
  private readonly CONSENT_STORAGE_KEY = 'error_reporting_consent';
  private readonly REPORTING_ENDPOINT = 'https://api.emailjs.com/api/v1.0/email/send';
  private readonly CONSENT_VERSION = '1.0';
  private sessionId: string;
  private hasUserConsent: boolean = false;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.loadErrorQueue();
    this.loadConsentStatus();
  }

  /**
   * Report an error with context (only if user has consented)
   */
  async reportError(
    error: Error | string,
    context: {
      component?: string;
      action?: string;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<void> {
    try {
      // Check if user has consented to error reporting
      if (!this.hasUserConsent) {
        logger.debug('Error reporting skipped - user has not consented');
        return;
      }

      const errorReport: ErrorReport = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        error: this.sanitizeErrorMessage(typeof error === 'string' ? error : error.message),
        stack: typeof error === 'string' ? undefined : this.sanitizeStackTrace(error.stack),
        sessionId: this.sessionId,
        platform: this.getPlatformInfo(),
        component: context.component,
        action: context.action,
        metadata: this.sanitizeMetadata(context.metadata),
      };

      // Add to queue
      this.errorQueue.push(errorReport);

      // Trim queue if too large
      if (this.errorQueue.length > this.MAX_QUEUE_SIZE) {
        this.errorQueue = this.errorQueue.slice(-this.MAX_QUEUE_SIZE);
      }

      // Save to storage
      await this.saveErrorQueue();

      // Log locally (sanitized)
      logger.info('Error reported and queued for sending');

      // Try to send immediately (non-blocking)
      this.sendErrorReport(errorReport).catch(err => {
        logger.error('Failed to send error report:', err);
      });

    } catch (err) {
      logger.error('Failed to report error:', err);
    }
  }

  /**
   * Request user consent for error reporting
   */
  async requestErrorReportingConsent(): Promise<boolean> {
    try {
      // This would typically show a user dialog
      // For now, we'll assume consent is granted if this method is called
      const consent: ErrorReportingConsent = {
        hasConsented: true,
        consentTimestamp: new Date().toISOString(),
        consentVersion: this.CONSENT_VERSION
      };

      await this.saveConsentStatus(consent);
      this.hasUserConsent = true;

      logger.info('User consented to error reporting');
      return true;
    } catch (error) {
      logger.error('Failed to save error reporting consent:', error);
      return false;
    }
  }

  /**
   * Revoke user consent for error reporting
   */
  async revokeErrorReportingConsent(): Promise<void> {
    try {
      const consent: ErrorReportingConsent = {
        hasConsented: false,
        consentTimestamp: new Date().toISOString(),
        consentVersion: this.CONSENT_VERSION
      };

      await this.saveConsentStatus(consent);
      this.hasUserConsent = false;

      // Clear existing error queue
      this.errorQueue = [];
      await this.saveErrorQueue();

      logger.info('User revoked consent for error reporting');
    } catch (error) {
      logger.error('Failed to revoke error reporting consent:', error);
    }
  }

  /**
   * Check if user has consented to error reporting
   */
  hasConsentedToErrorReporting(): boolean {
    return this.hasUserConsent;
  }

  /**
   * Generate anonymous session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get generic platform information (no personal data)
   */
  private getPlatformInfo(): string {
    try {
      if (typeof window !== 'undefined') {
        return 'web';
      } else {
        return 'mobile';
      }
    } catch {
      return 'unknown';
    }
  }

  /**
   * Request user consent for error reporting
   */
  async requestErrorReportingConsent(): Promise<boolean> {
    try {
      const consent: ErrorReportingConsent = {
        hasConsented: true,
        consentTimestamp: new Date().toISOString(),
        consentVersion: this.CONSENT_VERSION
      };

      await this.saveConsentStatus(consent);
      this.hasUserConsent = true;

      logger.info('User consented to error reporting');
      return true;
    } catch (error) {
      logger.error('Failed to save error reporting consent:', error);
      return false;
    }
  }

  /**
   * Revoke user consent for error reporting
   */
  async revokeErrorReportingConsent(): Promise<void> {
    try {
      const consent: ErrorReportingConsent = {
        hasConsented: false,
        consentTimestamp: new Date().toISOString(),
        consentVersion: this.CONSENT_VERSION
      };

      await this.saveConsentStatus(consent);
      this.hasUserConsent = false;

      // Clear existing error queue
      this.errorQueue = [];
      await this.saveErrorQueue();

      logger.info('User revoked consent for error reporting');
    } catch (error) {
      logger.error('Failed to revoke error reporting consent:', error);
    }
  }

  /**
   * Check if user has consented to error reporting
   */
  hasConsentedToErrorReporting(): boolean {
    return this.hasUserConsent;
  }

  /**
   * Generate anonymous session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get generic platform information (no personal data)
   */
  private getPlatformInfo(): string {
    try {
      if (typeof window !== 'undefined') {
        return 'web';
      } else {
        return 'mobile';
      }
    } catch {
      return 'unknown';
    }
  }

  /**
   * Sanitize error message to remove potential PII
   */
  private sanitizeErrorMessage(message: string): string {
    if (!message) return '';

    // Remove email addresses
    let sanitized = message.replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REDACTED]');

    // Remove phone numbers (basic patterns)
    sanitized = sanitized.replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, '[PHONE_REDACTED]');

    // Remove potential user IDs (UUIDs and similar patterns)
    sanitized = sanitized.replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, '[ID_REDACTED]');

    // Remove file paths that might contain usernames
    sanitized = sanitized.replace(/\/Users\/<USER>\/\s]+/g, '/Users/<USER>');
    sanitized = sanitized.replace(/C:\\Users\\<USER>\\s]+/g, 'C:\\Users\\<USER>