import { supabase } from '../lib/supabase/client';
import { logger } from '../utils/logger';

export interface FavoriteItem {
  id: string;
  type: 'date_night' | 'meal';
  user_id: string;
  item_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateFavoriteData {
  type: 'date_night' | 'meal';
  user_id: string;
  item_id: string;
}

class FavoritesService {
  /**
   * Add an item to favorites
   */
  async addFavorite(data: CreateFavoriteData): Promise<FavoriteItem> {
    try {
      const { data: favorite, error } = await supabase
        .from('favorites')
        .insert([data])
        .select()
        .single();

      if (error) {
        logger.error('Error adding favorite:', error);
        throw new Error(`Failed to add favorite: ${error.message}`);
      }

      return favorite;
    } catch (error) {
      logger.error('Error in addFavorite:', error);
      throw error;
    }
  }

  /**
   * Remove an item from favorites
   */
  async removeFavorite(userId: string, itemId: string, type: 'date_night' | 'meal'): Promise<void> {
    try {
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('type', type);

      if (error) {
        logger.error('Error removing favorite:', error);
        throw new Error(`Failed to remove favorite: ${error.message}`);
      }
    } catch (error) {
      logger.error('Error in removeFavorite:', error);
      throw error;
    }
  }

  /**
   * Check if an item is favorited by a user
   */
  async isFavorited(userId: string, itemId: string, type: 'date_night' | 'meal'): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('type', type)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        logger.error('Error checking favorite status:', error);
        throw new Error(`Failed to check favorite status: ${error.message}`);
      }

      return !!data;
    } catch (error) {
      logger.error('Error in isFavorited:', error);
      throw error;
    }
  }

  /**
   * Get all favorites for a user by type
   */
  async getUserFavorites(userId: string, type: 'date_night' | 'meal'): Promise<FavoriteItem[]> {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('*')
        .eq('user_id', userId)
        .eq('type', type)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error getting user favorites:', error);
        throw new Error(`Failed to get favorites: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getUserFavorites:', error);
      throw error;
    }
  }

  /**
   * Toggle favorite status for an item
   */
  async toggleFavorite(
    userId: string,
    itemId: string,
    type: 'date_night' | 'meal',
    isFavorited: boolean
  ): Promise<void> {
    try {
      if (!userId || !itemId || !type) {
        throw new Error('Missing required parameters for toggleFavorite');
      }

      if (isFavorited) {
        await this.addFavorite({ user_id: userId, item_id: itemId, type });
      } else {
        await this.removeFavorite(userId, itemId, type);
      }
    } catch (error) {
      logger.error('Error in toggleFavorite:', error);
      throw error;
    }
  }

  /**
   * Get favorite item IDs for a user by type
   */
  async getFavoriteItemIds(userId: string, type: 'date_night' | 'meal'): Promise<string[]> {
    try {
      const favorites = await this.getUserFavorites(userId, type);
      return favorites.map(fav => fav.item_id);
    } catch (error) {
      logger.error('Error in getFavoriteItemIds:', error);
      throw error;
    }
  }
}

export const favoritesService = new FavoritesService();
export default favoritesService;
