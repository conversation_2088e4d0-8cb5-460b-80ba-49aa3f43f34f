import { supabase, getCurrentUser } from '../lib/supabase/client';
import { logger } from '../utils/logger';

export interface WeeklyDataRecord {
  id?: string;
  userId: string;
  weekNumber: number;
  data: any;
  completedSections: boolean[];
  completedAt?: number;
  createdAt?: number;
  updatedAt?: number;
}

export interface OriginStoryRecord {
  id?: string;
  userId: string;
  data: any;
  lastUpdated: number;
  createdAt?: number;
  updatedAt?: number;
}

export interface PointsSystemRecord {
  id?: string;
  userId: string;
  totalPoints: number;
  level: number;
  achievements: any[];
  lastActivity: number;
  createdAt?: number;
  updatedAt?: number;
}

class DataService {
  /**
   * Get current user ID
   */
  private async getCurrentUserId(): Promise<string | null> {
    try {
      const user = await getCurrentUser();
      return user?.id || null;
    } catch (error) {
      logger.error('Get current user ID error:', error);
      return null;
    }
  }

  /**
   * Weekly Data Operations
   */
  async getWeeklyData(weekNumber: number): Promise<WeeklyDataRecord | null> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for getWeeklyData');
        return null;
      }

      const { data, error } = await supabase
        .from('weekly_data')
        .select('*')
        .eq('user_id', userId)
        .eq('week_number', weekNumber)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Get weekly data error:', error);
        return null;
      }

      if (!data) {
        return null;
      }

      return {
        id: data.id,
        userId: data.user_id,
        weekNumber: data.week_number,
        data: data.data,
        completedSections: data.completed_sections,
        completedAt: data.completed_at ? new Date(data.completed_at).getTime() : undefined,
        createdAt: new Date(data.created_at).getTime(),
        updatedAt: new Date(data.updated_at).getTime(),
      };
    } catch (error) {
      logger.error('Get weekly data error:', error);
      return null;
    }
  }

  async saveWeeklyData(weekNumber: number, data: any, completedSections: boolean[], completedAt?: number): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for saveWeeklyData');
        return false;
      }

      const recordData = {
        user_id: userId,
        week_number: weekNumber,
        data,
        completed_sections: completedSections,
        completed_at: completedAt ? new Date(completedAt).toISOString() : null,
      };

      const { error } = await supabase
        .from('weekly_data')
        .upsert(recordData, {
          onConflict: 'user_id,week_number',
        });

      if (error) {
        logger.error('Save weekly data error:', error);
        return false;
      }

      logger.info('Weekly data saved successfully:', { weekNumber, userId });
      return true;
    } catch (error) {
      logger.error('Save weekly data error:', error);
      return false;
    }
  }

  /**
   * Origin Story Operations
   */
  async getOriginStory(): Promise<OriginStoryRecord | null> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for getOriginStory');
        return null;
      }

      const { data, error } = await supabase
        .from('origin_story')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Get origin story error:', error);
        return null;
      }

      if (!data) {
        return null;
      }

      return {
        id: data.id,
        userId: data.user_id,
        data: data.data,
        lastUpdated: new Date(data.last_updated).getTime(),
        createdAt: new Date(data.created_at).getTime(),
        updatedAt: new Date(data.updated_at).getTime(),
      };
    } catch (error) {
      logger.error('Get origin story error:', error);
      return null;
    }
  }

  async saveOriginStory(data: any): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for saveOriginStory');
        return false;
      }

      const recordData = {
        user_id: userId,
        data,
        last_updated: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('origin_story')
        .upsert(recordData, {
          onConflict: 'user_id',
        });

      if (error) {
        logger.error('Save origin story error:', error);
        return false;
      }

      logger.info('Origin story saved successfully:', { userId });
      return true;
    } catch (error) {
      logger.error('Save origin story error:', error);
      return false;
    }
  }

  /**
   * Points System Operations
   */
  async getPointsSystem(): Promise<PointsSystemRecord | null> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for getPointsSystem');
        return null;
      }

      const { data, error } = await supabase
        .from('points_system')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Get points system error:', error);
        return null;
      }

      if (!data) {
        return null;
      }

      return {
        id: data.id,
        userId: data.user_id,
        totalPoints: data.total_points,
        level: data.level,
        achievements: data.achievements,
        lastActivity: new Date(data.last_activity).getTime(),
        createdAt: new Date(data.created_at).getTime(),
        updatedAt: new Date(data.updated_at).getTime(),
      };
    } catch (error) {
      logger.error('Get points system error:', error);
      return null;
    }
  }

  async savePointsSystem(totalPoints: number, level: number, achievements: any[]): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for savePointsSystem');
        return false;
      }

      const recordData = {
        user_id: userId,
        total_points: totalPoints,
        level,
        achievements,
        last_activity: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('points_system')
        .upsert(recordData, {
          onConflict: 'user_id',
        });

      if (error) {
        logger.error('Save points system error:', error);
        return false;
      }

      logger.info('Points system saved successfully:', { userId, totalPoints, level });
      return true;
    } catch (error) {
      logger.error('Save points system error:', error);
      return false;
    }
  }


  /**
   * Meal Voting Operations
   */
  async getMealVoting(): Promise<any> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for getMealVoting');
        return null;
      }

      const { data, error } = await supabase
        .from('meal_voting')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Get meal voting error:', error);
        return null;
      }

      return data || { history: [], preferences: {} };
    } catch (error) {
      logger.error('Get meal voting error:', error);
      return null;
    }
  }

  async saveMealVoting(history: any[], preferences: any = {}): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for saveMealVoting');
        return false;
      }

      const recordData = {
        user_id: userId,
        history,
        preferences,
      };

      const { error } = await supabase
        .from('meal_voting')
        .upsert(recordData, {
          onConflict: 'user_id',
        });

      if (error) {
        logger.error('Save meal voting error:', error);
        return false;
      }

      logger.info('Meal voting saved successfully:', { userId });
      return true;
    } catch (error) {
      logger.error('Save meal voting error:', error);
      return false;
    }
  }

  /**
   * Scrapbook Operations
   */
  async getScrapbook(): Promise<any[]> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for getScrapbook');
        return [];
      }

      const { data, error } = await supabase
        .from('scrapbook')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Get scrapbook error:', error);
        return [];
      }

      return data?.entries || [];
    } catch (error) {
      logger.error('Get scrapbook error:', error);
      return [];
    }
  }

  async saveScrapbook(entries: any[]): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        logger.warn('No user ID available for saveScrapbook');
        return false;
      }

      const recordData = {
        user_id: userId,
        entries,
      };

      const { error } = await supabase
        .from('scrapbook')
        .upsert(recordData, {
          onConflict: 'user_id',
        });

      if (error) {
        logger.error('Save scrapbook error:', error);
        return false;
      }

      logger.info('Scrapbook saved successfully:', { userId });
      return true;
    } catch (error) {
      logger.error('Save scrapbook error:', error);
      return false;
    }
  }

  /**
   * Sync all data from local storage to Supabase
   */
  async syncAllData(): Promise<boolean> {
    try {
      logger.info('Starting data sync to Supabase');
      
      // This would be called after user signs in to sync any local data
      // Implementation depends on your specific needs
      
      logger.info('Data sync completed');
      return true;
    } catch (error) {
      logger.error('Data sync error:', error);
      return false;
    }
  }
}

export const dataService = new DataService();
export default dataService;
