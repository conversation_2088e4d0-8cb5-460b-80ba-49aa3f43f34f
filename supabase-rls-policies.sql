-- Row Level Security Policies
-- Run this AFTER creating the tables

-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE weekly_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE origin_story ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_system ENABLE ROW LEVEL SECURITY;
ALTER TABLE date_night_ideas ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_voting ENABLE ROW LEVEL SECURITY;
ALTER TABLE scrapbook ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Create RLS policies for weekly_data
CREATE POLICY "Users can view own weekly data" ON weekly_data
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own weekly data" ON weekly_data
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own weekly data" ON weekly_data
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for origin_story
CREATE POLICY "Users can view own origin story" ON origin_story
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own origin story" ON origin_story
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own origin story" ON origin_story
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for points_system
CREATE POLICY "Users can view own points" ON points_system
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own points" ON points_system
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own points" ON points_system
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for date_night_ideas
CREATE POLICY "Users can view own date night ideas" ON date_night_ideas
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own date night ideas" ON date_night_ideas
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own date night ideas" ON date_night_ideas
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for meal_voting
CREATE POLICY "Users can view own meal voting" ON meal_voting
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own meal voting" ON meal_voting
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own meal voting" ON meal_voting
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for scrapbook
CREATE POLICY "Users can view own scrapbook" ON scrapbook
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own scrapbook" ON scrapbook
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own scrapbook" ON scrapbook
  FOR INSERT WITH CHECK (auth.uid() = user_id);
