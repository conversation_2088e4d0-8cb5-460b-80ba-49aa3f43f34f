#!/usr/bin/env node

// Test script to verify secure storage encryption
console.log('Testing secure storage encryption...');

// Mock Platform for testing
global.Platform = { OS: 'web' };

// Mock window and localStorage for testing
global.window = {
  localStorage: {
    data: {},
    setItem(key, value) {
      this.data[key] = value;
      console.log(`localStorage.setItem: ${key} = ${value.substring(0, 50)}...`);
    },
    getItem(key) {
      const value = this.data[key] || null;
      console.log(`localStorage.getItem: ${key} = ${value ? value.substring(0, 50) + '...' : 'null'}`);
      return value;
    },
    removeItem(key) {
      delete this.data[key];
      console.log(`localStorage.removeItem: ${key}`);
    },
    key(index) {
      return Object.keys(this.data)[index] || null;
    },
    get length() {
      return Object.keys(this.data).length;
    }
  },
  navigator: { userAgent: 'test-browser' },
  btoa: (str) => Buffer.from(str, 'binary').toString('base64'),
  atob: (str) => Buffer.from(str, 'base64').toString('binary'),
  TextEncoder: class {
    encode(str) {
      return Buffer.from(str, 'utf8');
    }
  },
  TextDecoder: class {
    decode(buffer) {
      return Buffer.from(buffer).toString('utf8');
    }
  }
};

// Make globals available
global.btoa = global.window.btoa;
global.atob = global.window.atob;
global.TextEncoder = global.window.TextEncoder;
global.TextDecoder = global.window.TextDecoder;
global.navigator = global.window.navigator;

async function testSecureStorage() {
  try {
    // Mock expo-crypto
    const mockCrypto = {
      digestStringAsync: async (algorithm, data) => {
        // Simple hash simulation
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(data).digest('hex');
      },
      CryptoDigestAlgorithm: {
        SHA256: 'SHA256'
      }
    };

    // Mock logger
    const mockLogger = {
      debug: (msg) => console.log(`[DEBUG] ${msg}`),
      error: (msg, error) => console.error(`[ERROR] ${msg}`, error),
      info: (msg) => console.log(`[INFO] ${msg}`)
    };

    // Inject mocks
    require.cache[require.resolve('expo-crypto')] = { exports: mockCrypto };
    require.cache[require.resolve('./utils/logger')] = { exports: { logger: mockLogger } };

    // Now test the SecureStorage
    const { SecureStorage } = require('./utils/secureStorage');
    const storage = SecureStorage.getInstance();

    console.log('\n1. Testing availability...');
    const isAvailable = await storage.isAvailable();
    console.log(`✅ Storage available: ${isAvailable}`);

    console.log('\n2. Testing data storage and retrieval...');
    const testData = { 
      name: 'Test User', 
      sensitive: 'secret_data_123',
      timestamp: Date.now()
    };

    await storage.setItem('test_key', testData);
    console.log('✅ Data stored successfully');

    const retrievedData = await storage.getItem('test_key');
    console.log('✅ Data retrieved successfully');
    console.log('Retrieved data:', retrievedData);

    // Verify data integrity
    if (JSON.stringify(testData) === JSON.stringify(retrievedData)) {
      console.log('✅ Data integrity verified');
    } else {
      console.log('❌ Data integrity check failed');
    }

    console.log('\n3. Testing data removal...');
    await storage.removeItem('test_key');
    const removedData = await storage.getItem('test_key');
    
    if (removedData === null) {
      console.log('✅ Data removal verified');
    } else {
      console.log('❌ Data removal failed');
    }

    console.log('\n4. Checking localStorage encryption...');
    await storage.setItem('encryption_test', 'plaintext_data');
    const rawStoredData = global.window.localStorage.getItem('encryption_test');
    
    if (rawStoredData !== 'plaintext_data' && rawStoredData !== '"plaintext_data"') {
      console.log('✅ Data is encrypted in localStorage');
    } else {
      console.log('❌ Data is not encrypted in localStorage');
    }

    console.log('\nSecure storage encryption tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSecureStorage();
