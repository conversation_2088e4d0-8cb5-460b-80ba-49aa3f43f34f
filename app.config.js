const IS_DEV = process.env.NODE_ENV === 'development';
const IS_PREVIEW = process.env.EAS_BUILD_PROFILE === 'preview';

export default {
  expo: {
    name: 'Everlasting Us',
    slug: 'everlasting-us',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/images/icon.png',
    scheme: 'everlasting-us',
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    splash: {
      image: './assets/images/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#F8BBD9'
    },
    assetBundlePatterns: ['**/*'],
    description: 'A secure relationship-building app for couples to strengthen their bond through guided activities and meaningful conversations.',
    keywords: [
      'relationship',
      'couples',
      'love',
      'communication',
      'activities',
      'dating',
      'romance',
      'connection'
    ],
    category: 'LIFESTYLE',
    privacy: 'unlisted',
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'com.everlasting.us',
      buildNumber: '1',
      infoPlist: {
        // Network Security Configuration - Production Safe
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: false,
          // Only allow localhost in development and preview builds
          ...(IS_DEV || IS_PREVIEW ? {
            NSExceptionDomains: {
              localhost: {
                NSExceptionAllowsInsecureHTTPLoads: true
              }
            }
          } : {})
        },
        NSCameraUsageDescription: 'This app uses the camera to capture relationship memories and moments.',
        NSPhotoLibraryUsageDescription: 'This app accesses your photo library to save relationship memories and moments.',
        CFBundleDisplayName: 'Everlasting Us',
        CFBundleName: 'Everlasting Us',
        CFBundleShortVersionString: '1.0.0',
        CFBundleVersion: '1',
        LSRequiresIPhoneOS: true,
        UILaunchStoryboardName: 'SplashScreen',
        UIRequiredDeviceCapabilities: ['armv7'],
        UISupportedInterfaceOrientations: ['UIInterfaceOrientationPortrait'],
        'UISupportedInterfaceOrientations~ipad': [
          'UIInterfaceOrientationPortrait',
          'UIInterfaceOrientationPortraitUpsideDown',
          'UIInterfaceOrientationLandscapeLeft',
          'UIInterfaceOrientationLandscapeRight'
        ]
      }
    },
    android: {
      package: 'com.everlasting.us',
      versionCode: 1,
      playStoreUrl: 'https://play.google.com/store/apps/details?id=com.everlasting.us',
      permissions: [
        'CAMERA',
        'READ_EXTERNAL_STORAGE',
        'WRITE_EXTERNAL_STORAGE'
      ],
      // Production-safe: No cleartext traffic allowed
      usesCleartextTraffic: false,
      adaptiveIcon: {
        foregroundImage: './assets/images/icon-foreground.png',
        backgroundColor: '#F8BBD9'
      },
      intentFilters: [
        {
          action: 'VIEW',
          autoVerify: true,
          data: [
            {
              scheme: 'https',
              host: 'everlasting.us'
            }
          ],
          category: ['BROWSABLE', 'DEFAULT']
        }
      ]
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/favicon.png'
    },
    plugins: [
      'expo-router',
      [
        'expo-image-picker',
        {
          photosPermission: 'This app accesses your photo library to save relationship memories and moments.',
          cameraPermission: 'This app uses the camera to capture relationship memories and moments.'
        }
      ],
      'expo-secure-store'
    ],
    experiments: {
      typedRoutes: true
    },
    extra: {
      router: {
        origin: false
      },
      eas: {
        projectId: 'your-project-id-here'
      }
    }
  }
};
