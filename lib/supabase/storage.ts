import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Custom storage adapter that works on both web and native
export const createStorageAdapter = () => {
  if (Platform.OS === 'web') {
    // For web, use localStorage directly
    return {
      getItem: async (key: string): Promise<string | null> => {
        try {
          if (typeof window !== 'undefined' && window.localStorage) {
            return window.localStorage.getItem(key);
          }
          return null;
        } catch (error) {
          console.warn('Error getting item from localStorage:', error);
          return null;
        }
      },
      setItem: async (key: string, value: string): Promise<void> => {
        try {
          if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.setItem(key, value);
          }
        } catch (error) {
          console.warn('Error setting item in localStorage:', error);
        }
      },
      removeItem: async (key: string): Promise<void> => {
        try {
          if (typeof window !== 'undefined' && window.localStorage) {
            window.localStorage.removeItem(key);
          }
        } catch (error) {
          console.warn('Error removing item from localStorage:', error);
        }
      },
    };
  } else {
    // For native platforms, use AsyncStorage
    return {
      getItem: async (key: string): Promise<string | null> => {
        try {
          return await AsyncStorage.getItem(key);
        } catch (error) {
          console.warn('Error getting item from AsyncStorage:', error);
          return null;
        }
      },
      setItem: async (key: string, value: string): Promise<void> => {
        try {
          await AsyncStorage.setItem(key, value);
        } catch (error) {
          console.warn('Error setting item in AsyncStorage:', error);
        }
      },
      removeItem: async (key: string): Promise<void> => {
        try {
          await AsyncStorage.removeItem(key);
        } catch (error) {
          console.warn('Error removing item from AsyncStorage:', error);
        }
      },
    };
  }
};
