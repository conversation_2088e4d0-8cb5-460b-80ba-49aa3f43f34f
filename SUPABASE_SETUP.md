# Supabase Environment Setup Guide

## Quick Setup

1. **Create a `.env` file** in your project root:
   ```bash
   touch .env
   ```

2. **Add your Supabase configuration** to the `.env` file:
   ```
   EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   ```

## How to Get Your Supabase Keys

### If you already have a Supabase project:
1. Go to [supabase.com](https://supabase.com)
2. Sign in to your account
3. Select your project
4. Go to **Settings** → **API**
5. Copy:
   - **Project URL** → `EXPO_PUBLIC_SUPABASE_URL`
   - **anon public** key → `EXPO_PUBLIC_SUPABASE_ANON_KEY`

### If you need to create a new Supabase project:
1. Go to [supabase.com](https://supabase.com)
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `everlasting-us`
   - **Database Password**: Choose a strong password
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for setup to complete (2-3 minutes)
7. Go to **Settings** → **API** to get your keys

## Security Notes

✅ **Safe to use in client-side code**: These are PUBLIC environment variables
✅ **Already in .gitignore**: Your `.env` file won't be committed to git
✅ **Anon key is safe**: It's designed for public access with Row Level Security

## Example .env file:
```
EXPO_PUBLIC_SUPABASE_URL=https://abcdefghijklmnop.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFiY2RlZmdoaWprbG1ub3AiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTYzNDU2Nzg5MCwiZXhwIjoxOTUwMTQzODkwfQ.example_signature_here
```

## After Setup

1. **Restart Expo**: Stop the current server (Ctrl+C) and run:
   ```bash
   npx expo start --clear
   ```

2. **Test the connection**: The app should now load without the Supabase error

## Troubleshooting

- **Still getting errors?** Make sure there are no spaces around the `=` in your `.env` file
- **Wrong URL format?** It should be `https://your-project-id.supabase.co`
- **Key too short?** The anon key should be a long JWT token (100+ characters)
