-- Triggers and Indexes
-- Run this AFTER creating tables and RLS policies

-- <PERSON><PERSON> function to handle updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON><PERSON> triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_weekly_data_updated_at BEFORE UPDATE ON weekly_data
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_origin_story_updated_at BEFORE UPDATE ON origin_story
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_points_system_updated_at BEFORE UPDATE ON points_system
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_date_night_ideas_updated_at BEFORE UPDATE ON date_night_ideas
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_meal_voting_updated_at BEFORE UPDATE ON meal_voting
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scrapbook_updated_at BEFORE UPDATE ON scrapbook
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_weekly_data_user_id ON weekly_data(user_id);
CREATE INDEX IF NOT EXISTS idx_weekly_data_week_number ON weekly_data(week_number);
CREATE INDEX IF NOT EXISTS idx_origin_story_user_id ON origin_story(user_id);
CREATE INDEX IF NOT EXISTS idx_points_system_user_id ON points_system(user_id);
CREATE INDEX IF NOT EXISTS idx_date_night_ideas_user_id ON date_night_ideas(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_voting_user_id ON meal_voting(user_id);
CREATE INDEX IF NOT EXISTS idx_scrapbook_user_id ON scrapbook(user_id);
