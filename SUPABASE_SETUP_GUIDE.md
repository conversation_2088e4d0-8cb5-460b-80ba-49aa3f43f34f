# Supabase Setup Guide for Everlasting Us

This guide will walk you through setting up Supabase as your backend for the Everlasting Us app.

## Prerequisites

- Supabase account (free tier available)
- Your Supabase project URL and anon key (already provided)

## Step 1: Database Setup

1. **Go to your Supabase Dashboard**
   - Visit [supabase.com](https://supabase.com)
   - Sign in to your account
   - Select your project

2. **Run the Database Schema**
   - Go to the SQL Editor in your Supabase dashboard
   - Copy the contents of `supabase-schema.sql` from this project
   - Paste it into the SQL Editor
   - Click "Run" to execute the schema

3. **Verify Tables Created**
   - Go to the Table Editor
   - You should see these tables:
     - `profiles`
     - `weekly_data`
     - `origin_story`
     - `points_system`
     - `date_night_ideas`
     - `meal_voting`
     - `scrapbook`

## Step 2: Authentication Setup

1. **Configure Authentication**
   - Go to Authentication > Settings in your Supabase dashboard
   - Enable Email authentication
   - Configure your site URL (for development: `http://localhost:8081`)
   - Add redirect URLs for your app

2. **Email Templates (Optional)**
   - Customize email templates in Authentication > Email Templates
   - Update the confirmation and reset password emails

## Step 3: Environment Configuration

Your Supabase credentials are already configured in the code:
- URL: `https://gvnizqgiqiotrysrvdkb.supabase.co`
- Anon Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## Step 4: Test the Setup

1. **Start your development server**
   ```bash
   npm run dev
   ```

2. **Test Authentication**
   - The app will show the AuthScreen when no user is logged in
   - Try creating a new account
   - Try signing in with existing credentials

3. **Test Data Sync**
   - Create some data in the app
   - Check your Supabase dashboard to see if data appears in the tables

## Step 5: Production Setup

1. **Update Environment Variables**
   - For production, use environment variables instead of hardcoded values
   - Set `EXPO_PUBLIC_SUPABASE_URL` and `EXPO_PUBLIC_SUPABASE_ANON_KEY`

2. **Configure Production URLs**
   - Update your Supabase project settings with production URLs
   - Configure proper redirect URLs for your production app

## Features Included

### 🔐 Authentication
- User registration and login
- Password reset functionality
- Secure session management
- Automatic token refresh

### 💾 Data Storage
- User profiles
- Weekly relationship data (12 weeks)
- Origin story data
- Points and achievements system
- Date night ideas
- Meal voting history
- Scrapbook entries

### 🔄 Hybrid Storage
- Automatic sync between local storage and Supabase
- Offline support with local fallback
- Data synchronization when coming back online

### 🛡️ Security
- Row Level Security (RLS) enabled on all tables
- Users can only access their own data
- Secure authentication with JWT tokens

## Database Schema Overview

### Tables Created

1. **profiles** - User profile information
2. **weekly_data** - Weekly relationship activities and progress
3. **origin_story** - Couple's origin story and memories
4. **points_system** - Points, levels, and achievements
5. **date_night_ideas** - Date night suggestions and favorites
6. **meal_voting** - Meal voting history and preferences
7. **scrapbook** - Photo and memory entries

### Security Features

- All tables have Row Level Security enabled
- Users can only access their own data
- Automatic timestamp updates
- Data validation and constraints

## Troubleshooting

### Common Issues

1. **Authentication not working**
   - Check your Supabase URL and anon key
   - Verify email authentication is enabled
   - Check redirect URLs in Supabase settings

2. **Data not syncing**
   - Check internet connection
   - Verify user is authenticated
   - Check browser console for errors

3. **Database errors**
   - Ensure schema was run successfully
   - Check RLS policies are in place
   - Verify table permissions

### Getting Help

- Check the Supabase documentation
- Review the app logs for error messages
- Test with a simple query in the Supabase SQL Editor

## Next Steps

1. **Customize the UI** - Modify the AuthScreen component to match your app's design
2. **Add more features** - Extend the data models as needed
3. **Set up monitoring** - Use Supabase's built-in monitoring tools
4. **Deploy to production** - Configure production environment variables

## Files Created/Modified

- `utils/supabase.ts` - Supabase client configuration
- `services/authService.ts` - Authentication service
- `services/dataService.ts` - Data management service
- `services/hybridStorageService.ts` - Hybrid local/cloud storage
- `components/AuthScreen.tsx` - Authentication UI
- `supabase-schema.sql` - Database schema

Your Supabase backend is now ready to use! 🎉
