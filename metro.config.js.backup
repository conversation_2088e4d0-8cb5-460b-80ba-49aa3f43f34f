const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Configure Metro to handle source maps better

// Optimize bundle performance
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Optimize resolver for faster builds
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.resolver.sourceExts = ['js', 'jsx', 'json', 'ts', 'tsx', 'mjs'];
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Additional resolver configuration to handle nested node_modules
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, 'node_modules'),
];

// Disable source maps in development for faster builds
if (process.env.NODE_ENV === 'development') {
  config.transformer.minifierConfig = {
    ...config.transformer.minifierConfig,
    drop_console: false,
  };
}

// Better error handling for symbolication
config.symbolicator = {
  customizeFrame: (frame) => {
    // Handle anonymous frames better
    if (frame.file === '<anonymous>' || !frame.file) {
      return {
        ...frame,
        file: 'Unknown',
        line: frame.line || 0,
        column: frame.column || 0,
      };
    }
    return frame;
  },
};

module.exports = config;
