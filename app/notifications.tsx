import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Bell, Clock, Heart, Calendar, MessageCircle, Star, Settings } from 'lucide-react-native';
import { router } from 'expo-router';
import { secureStorage } from '../utils/secureStorage';
import HamburgerMenu from '../components/HamburgerMenu';

interface NotificationSettings {
  dailyReminders: boolean;
  weeklyProgress: boolean;
  newActivities: boolean;
  milestoneCelebrations: boolean;
  relationshipTips: boolean;
  quietHours: boolean;
  quietStart: string;
  quietEnd: string;
}

const NOTIFICATION_SETTINGS_KEY = 'notification_settings';

const DEFAULT_NOTIFICATIONS: NotificationSettings = {
  dailyReminders: true,
  weeklyProgress: true,
  newActivities: true,
  milestoneCelebrations: true,
  relationshipTips: false,
  quietHours: false,
  quietStart: '22:00',
  quietEnd: '08:00',
};

export default function NotificationsScreen() {
  const [settings, setSettings] = useState<NotificationSettings>(DEFAULT_NOTIFICATIONS);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadNotificationSettings();
  }, []);

  const loadNotificationSettings = async () => {
    try {
      const stored = await secureStorage.getItem<string>(NOTIFICATION_SETTINGS_KEY);
      if (stored) {
        setSettings({ ...DEFAULT_NOTIFICATIONS, ...JSON.parse(stored) });
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveNotificationSettings = async (newSettings: NotificationSettings) => {
    try {
      await secureStorage.setItem(NOTIFICATION_SETTINGS_KEY, JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
      Alert.alert('Error', 'Failed to save notification settings. Please try again.');
    }
  };

  const toggleSetting = async (key: keyof NotificationSettings) => {
    const newSettings = { ...settings, [key]: !settings[key] };
    await saveNotificationSettings(newSettings);
  };

  const changeQuietHours = () => {
    Alert.alert(
      'Quiet Hours',
      'Set the time range when notifications will be silenced:',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: '10 PM - 8 AM', 
          onPress: () => saveNotificationSettings({ ...settings, quietStart: '22:00', quietEnd: '08:00' })
        },
        { 
          text: '11 PM - 7 AM', 
          onPress: () => saveNotificationSettings({ ...settings, quietStart: '23:00', quietEnd: '07:00' })
        },
        { 
          text: '12 AM - 6 AM', 
          onPress: () => saveNotificationSettings({ ...settings, quietStart: '00:00', quietEnd: '06:00' })
        },
        { 
          text: 'Custom', 
          onPress: () => Alert.alert('Coming Soon', 'Custom quiet hours will be available in a future update.') 
        },
      ]
    );
  };

  const testNotification = () => {
    Alert.alert(
      'Test Notification',
      'This would send a test notification to verify your settings are working correctly.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Send Test', onPress: () => Alert.alert('Success', 'Test notification sent!') },
      ]
    );
  };

  const renderNotificationItem = (
    icon: React.ReactNode,
    title: string,
    subtitle: string,
    value: boolean,
    onToggle: () => void
  ) => (
    <View style={styles.notificationItem}>
      <View style={styles.notificationLeft}>
        <View style={styles.notificationIcon}>
          {icon}
        </View>
        <View style={styles.notificationText}>
          <Text style={styles.notificationTitle}>{title}</Text>
          <Text style={styles.notificationSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: '#E5E7EB', true: '#F8BBD9' }}
        thumbColor={value ? '#E879F9' : '#9CA3AF'}
      />
    </View>
  );

  const renderTimeSetting = (
    icon: React.ReactNode,
    title: string,
    subtitle: string,
    value: string,
    onPress: () => void
  ) => (
    <TouchableOpacity style={styles.notificationItem} onPress={onPress}>
      <View style={styles.notificationLeft}>
        <View style={styles.notificationIcon}>
          {icon}
        </View>
        <View style={styles.notificationText}>
          <Text style={styles.notificationTitle}>{title}</Text>
          <Text style={styles.notificationSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <View style={styles.timeDisplay}>
        <Text style={styles.timeText}>{value}</Text>
        <Text style={styles.timeArrow}>›</Text>
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading notifications...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />
      
      {/* Header */}
      <LinearGradient
        colors={['#F8BBD9', '#E879F9']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Daily Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Daily Notifications</Text>
          {renderNotificationItem(
            <Clock size={20} color="#F8BBD9" />,
            'Daily Reminders',
            'Get reminded to spend time together',
            settings.dailyReminders,
            () => toggleSetting('dailyReminders')
          )}
          {renderNotificationItem(
            <Calendar size={20} color="#E879F9" />,
            'Weekly Progress',
            'Weekly summary of your relationship journey',
            settings.weeklyProgress,
            () => toggleSetting('weeklyProgress')
          )}
        </View>

        {/* Activity Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Activity Notifications</Text>
          {renderNotificationItem(
            <MessageCircle size={20} color="#60A5FA" />,
            'New Activities',
            'When new activities become available',
            settings.newActivities,
            () => toggleSetting('newActivities')
          )}
          {renderNotificationItem(
            <Star size={20} color="#F59E0B" />,
            'Milestone Celebrations',
            'Celebrate your achievements together',
            settings.milestoneCelebrations,
            () => toggleSetting('milestoneCelebrations')
          )}
          {renderNotificationItem(
            <Heart size={20} color="#10B981" />,
            'Relationship Tips',
            'Weekly tips for strengthening your bond',
            settings.relationshipTips,
            () => toggleSetting('relationshipTips')
          )}
        </View>

        {/* Quiet Hours */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quiet Hours</Text>
          {renderNotificationItem(
            <Settings size={20} color="#8B5CF6" />,
            'Enable Quiet Hours',
            'Silence notifications during specific times',
            settings.quietHours,
            () => toggleSetting('quietHours')
          )}
          {settings.quietHours && (
            renderTimeSetting(
              <Clock size={20} color="#8B5CF6" />,
              'Quiet Hours',
              'Set your preferred quiet time range',
              `${settings.quietStart} - ${settings.quietEnd}`,
              changeQuietHours
            )
          )}
        </View>

        {/* Test & Actions */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.testButton} onPress={testNotification}>
            <Bell size={20} color="#FFFFFF" />
            <Text style={styles.testButtonText}>Test Notifications</Text>
          </TouchableOpacity>
        </View>

        {/* Info */}
        <View style={styles.infoCard}>
          <Bell size={24} color="#9CA3AF" />
          <Text style={styles.infoTitle}>Notification Preferences</Text>
          <Text style={styles.infoText}>
            Your notification settings are saved automatically. You can change these anytime to suit your preferences.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3E2',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  notificationLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  notificationText: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  notificationSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  timeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    color: '#6B7280',
    marginRight: 8,
  },
  timeArrow: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  testButton: {
    backgroundColor: '#F8BBD9',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});
