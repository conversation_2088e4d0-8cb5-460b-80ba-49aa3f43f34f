import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Play, Calendar, MapPin, Plane, Umbrella, Shield, Handshake, Clock, Star } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekFiveData } from '../hooks/useWeekFiveData';
import { colors } from '../utils/colors';

export default function WeekFiveScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentChatIndex, setCurrentChatIndex] = useState(0);
  const [isPlayerOneTurn, setIsPlayerOneTurn] = useState(true);
  const [flipAnimation] = useState(new Animated.Value(0));
  const [isFlipped, setIsFlipped] = useState(false);
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateDreamVacationAdjectives,
    updateDreamVacationPlan,
    updateChatPrompt,
    updateConflictStyleReflection,
    updateCompletedSections,
  } = useWeekFiveData();

  const steps = [
    { title: 'Dream Vacation', icon: <Umbrella size={24} color={colors.white} /> },
    { title: 'Date Night Plan', icon: <Calendar size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Conflict Style', icon: <Shield size={24} color={colors.white} /> },
  ];

  const conflictStyles = [
    { name: 'Avoiding', description: 'Hoping issues go away', icon: <Clock size={20} color={colors.textSecondary} />, color: colors.backgroundTertiary },
    { name: 'Competing', description: 'Needing to win', icon: <Shield size={20} color={colors.redDark} />, color: colors.backgroundGray },
    { name: 'Accommodating', description: 'Prioritizing peace', icon: <Heart size={20} color={colors.warning} />, color: colors.backgroundOrange },
    { name: 'Compromising', description: 'Meeting halfway', icon: <Star size={20} color={colors.purple} />, color: colors.backgroundPink },
    { name: 'Collaborating', description: 'Finding solutions together', icon: <Handshake size={20} color={colors.success} />, color: colors.backgroundGray },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Five! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleAdjectiveChange = (player: 'playerOne' | 'playerTwo', index: number, value: string) => {
    const currentAdjectives = player === 'playerOne' 
      ? [...data.dreamVacationAdjectives.playerOneAdjectives]
      : [...data.dreamVacationAdjectives.playerTwoAdjectives];
    
    currentAdjectives[index] = value;
    updateDreamVacationAdjectives(player, currentAdjectives);
  };

  const handleVacationPlanChange = (field: keyof typeof data.dreamVacationPlan, value: string | boolean) => {
    updateDreamVacationPlan({ [field]: value });
  };

  const handleChatPromptChange = (text: string) => {
    if (isPlayerOneTurn) {
      updateChatPrompt(currentChatIndex, 'playerOne', text);
    } else {
      updateChatPrompt(currentChatIndex, 'playerTwo', text);
    }
  };

  const handleNextChatPrompt = () => {
    if (currentChatIndex < data.chatPrompts.length - 1) {
      setCurrentChatIndex(currentChatIndex + 1);
      setIsPlayerOneTurn(!isPlayerOneTurn);
    } else {
      // Chat prompts completed
      const newSections = [...data.completedSections];
      newSections[2] = true;
      updateCompletedSections(newSections);
    }
  };

  const handleConflictStyleSelection = (player: 'playerOne' | 'playerTwo', style: string) => {
    const currentStyles = player === 'playerOne' 
      ? [...data.conflictStyleReflection.playerOneStyles]
      : [...data.conflictStyleReflection.playerTwoStyles];
    
    const index = currentStyles.indexOf(style);
    if (index > -1) {
      currentStyles.splice(index, 1);
    } else {
      currentStyles.push(style);
    }
    
    if (player === 'playerOne') {
      updateConflictStyleReflection({ playerOneStyles: currentStyles });
    } else {
      updateConflictStyleReflection({ playerTwoStyles: currentStyles });
    }
  };

  const handleFlipCard = () => {
    const newValue = isFlipped ? 0 : 1;
    setIsFlipped(!isFlipped);
    Animated.spring(flipAnimation, {
      toValue: newValue,
      useNativeDriver: true,
    }).start();
  };

  const renderDreamVacation = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Dream Vacation</Text>
          <Text style={styles.sectionSubtitle}>
            Vacations aren't just getaways — they can strengthen your bond. Describe your perfect vacation using 5 adjectives each.
          </Text>
        </View>

        <View style={styles.adjectivesContainer}>
          <View style={styles.playerSection}>
            <Text style={styles.playerTitle}>Player One</Text>
            <View style={styles.adjectivesInputs}>
              {data.dreamVacationAdjectives.playerOneAdjectives.map((adj, index) => (
                <TextInput
                  key={index}
                  style={styles.adjectiveInput}
                  placeholder={`Adjective ${index + 1}`}
                  value={adj}
                  onChangeText={(text) => handleAdjectiveChange('playerOne', index, text)}
                  placeholderTextColor="colors.textTertiary"
                />
              ))}
            </View>
          </View>

          <View style={styles.playerSection}>
            <Text style={styles.playerTitle}>Player Two</Text>
            <View style={styles.adjectivesInputs}>
              {data.dreamVacationAdjectives.playerTwoAdjectives.map((adj, index) => (
                <TextInput
                  key={index}
                  style={styles.adjectiveInput}
                  placeholder={`Adjective ${index + 1}`}
                  value={adj}
                  onChangeText={(text) => handleAdjectiveChange('playerTwo', index, text)}
                  placeholderTextColor="colors.textTertiary"
                />
              ))}
            </View>
          </View>
        </View>

        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Your Dream Vacation Vibes</Text>
          <View style={styles.resultsRow}>
            <View style={styles.resultsColumn}>
              <Text style={styles.resultsLabel}>Player One:</Text>
              <Text style={styles.resultsText}>
                {data.dreamVacationAdjectives.playerOneAdjectives.filter(adj => adj.trim()).join(', ') || 'Add your adjectives above'}
              </Text>
            </View>
            <View style={styles.resultsColumn}>
              <Text style={styles.resultsLabel}>Player Two:</Text>
              <Text style={styles.resultsText}>
                {data.dreamVacationAdjectives.playerTwoAdjectives.filter(adj => adj.trim()).join(', ') || 'Add your adjectives above'}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderDateNightPlan = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Dream Vacation Planning</Text>
          <Text style={styles.sectionSubtitle}>
            Based on your adjectives, research your ultimate dream vacation. No limits — just fun planning together!
          </Text>
        </View>

        <View style={styles.planningContainer}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>When?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Summer 2024, Next year..."
              value={data.dreamVacationPlan.when}
              onChangeText={(text) => handleVacationPlanChange('when', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Where in the world?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Bali, Italy, Road trip across USA..."
              value={data.dreamVacationPlan.worldLocation}
              onChangeText={(text) => handleVacationPlanChange('worldLocation', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>What kind of vibe?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Relaxing, Adventure, Cultural, Romantic..."
              value={data.dreamVacationPlan.vibe}
              onChangeText={(text) => handleVacationPlanChange('vibe', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Who's coming along?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Just us, Family, Friends..."
              value={data.dreamVacationPlan.companions}
              onChangeText={(text) => handleVacationPlanChange('companions', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Ideal accommodation?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Beachfront villa, Cozy B&B, Luxury resort..."
              value={data.dreamVacationPlan.accommodation}
              onChangeText={(text) => handleVacationPlanChange('accommodation', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>How will you spend your days?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Exploring, Relaxing, Learning, Adventure..."
              value={data.dreamVacationPlan.dailyActivities}
              onChangeText={(text) => handleVacationPlanChange('dailyActivities', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Ideal weather?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Sunny and warm, Cool and crisp, Tropical..."
              value={data.dreamVacationPlan.weather}
              onChangeText={(text) => handleVacationPlanChange('weather', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Activities?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Hiking, Swimming, Cooking classes, Museums..."
              value={data.dreamVacationPlan.activities}
              onChangeText={(text) => handleVacationPlanChange('activities', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Food situation?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Local cuisine, Fine dining, Street food, Cooking together..."
              value={data.dreamVacationPlan.food}
              onChangeText={(text) => handleVacationPlanChange('food', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Splurge level?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Budget-friendly, Mid-range, Luxury, Once-in-a-lifetime..."
              value={data.dreamVacationPlan.splurgeLevel}
              onChangeText={(text) => handleVacationPlanChange('splurgeLevel', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Bucket list item?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., See the Northern Lights, Visit Machu Picchu, Learn to surf..."
              value={data.dreamVacationPlan.bucketListItem}
              onChangeText={(text) => handleVacationPlanChange('bucketListItem', text)}
              placeholderTextColor="colors.textTertiary"
            />
          </View>
        </View>

        <TouchableOpacity
          style={styles.completeButton}
          onPress={() => handleVacationPlanChange('completed', !data.dreamVacationPlan.completed)}
        >
          <LinearGradient
            colors={data.dreamVacationPlan.completed ? [colors.success, colors.greenDark] : [colors.warning, colors.orangeDark]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.completeButtonGradient}
          >
            <Text style={styles.completeButtonText}>
              {data.dreamVacationPlan.completed ? '✓ Completed' : 'Mark as Completed'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };

  const renderChatPrompts = () => {
    const currentPrompt = data.chatPrompts[currentChatIndex];
    
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Deep Conversations</Text>
          <Text style={styles.sectionSubtitle}>
            Take turns answering these thought-provoking questions
          </Text>
        </View>

        <View style={styles.chatContainer}>
          <TouchableOpacity style={styles.flipCard} onPress={handleFlipCard}>
            <Animated.View
              style={[
                styles.flipCardInner,
                {
                  transform: [{
                    rotateY: flipAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '180deg'],
                    })
                  }]
                }
              ]}
            >
              <View style={styles.flipCardFront}>
                <Text style={styles.flipCardText}>Tap to reveal question</Text>
                <MessageCircle size={32} color={colors.warning} />
              </View>
              <View style={styles.flipCardBack}>
                <Text style={styles.flipCardQuestion}>{currentPrompt.prompt}</Text>
              </View>
            </Animated.View>
          </TouchableOpacity>

          <View style={styles.chatInputContainer}>
            <Text style={styles.chatInputLabel}>
              {isPlayerOneTurn ? 'Player One' : 'Player Two'}'s Answer:
            </Text>
            <TextInput
              style={styles.chatTextInput}
              placeholder="Share your thoughts..."
              value={isPlayerOneTurn ? currentPrompt.playerOneAnswer : currentPrompt.playerTwoAnswer}
              onChangeText={handleChatPromptChange}
              placeholderTextColor="colors.textTertiary"
              multiline
            />
          </View>

          <TouchableOpacity
            style={styles.nextButton}
            onPress={handleNextChatPrompt}
            disabled={currentChatIndex >= data.chatPrompts.length - 1}
          >
            <LinearGradient
              colors={currentChatIndex >= data.chatPrompts.length - 1 ? [colors.textTertiary, colors.textSecondary] : [colors.warning, colors.orangeDark]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.nextButtonGradient}
            >
              <Text style={styles.nextButtonText}>
                {currentChatIndex >= data.chatPrompts.length - 1 ? 'All Done!' : 'Next Question'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderConflictStyle = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Conflict Style Awareness</Text>
          <Text style={styles.sectionSubtitle}>
            Discover your conflict resolution styles and explore alternatives
          </Text>
        </View>

        <View style={styles.conflictStylesContainer}>
          <Text style={styles.conflictStylesTitle}>The 5 Conflict Styles</Text>
          {conflictStyles.map((style, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.conflictStyleCard,
                { backgroundColor: style.color },
                (data.conflictStyleReflection.playerOneStyles.includes(style.name) ||
                 data.conflictStyleReflection.playerTwoStyles.includes(style.name)) && styles.conflictStyleSelected
              ]}
              onPress={() => {
                if (isPlayerOneTurn) {
                  handleConflictStyleSelection('playerOne', style.name);
                } else {
                  handleConflictStyleSelection('playerTwo', style.name);
                }
              }}
            >
              <View style={styles.conflictStyleHeader}>
                {style.icon}
                <Text style={styles.conflictStyleName}>{style.name}</Text>
              </View>
              <Text style={styles.conflictStyleDescription}>{style.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.reflectionContainer}>
          <Text style={styles.reflectionTitle}>Reflection Questions</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>What's your usual conflict style?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Share your thoughts..."
              value={isPlayerOneTurn ? data.conflictStyleReflection.playerOneReflection : data.conflictStyleReflection.playerTwoReflection}
              onChangeText={(text) => {
                if (isPlayerOneTurn) {
                  updateConflictStyleReflection({ playerOneReflection: text });
                } else {
                  updateConflictStyleReflection({ playerTwoReflection: text });
                }
              }}
              placeholderTextColor="colors.textTertiary"
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>What's one alternative style you could try next time?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Share your thoughts..."
              value={data.conflictStyleReflection.alternativeStyle}
              onChangeText={(text) => updateConflictStyleReflection({ alternativeStyle: text })}
              placeholderTextColor="colors.textTertiary"
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>How did it feel to experiment with a different style?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Share your thoughts..."
              value={data.conflictStyleReflection.experimentFeelings}
              onChangeText={(text) => updateConflictStyleReflection({ experimentFeelings: text })}
              placeholderTextColor="colors.textTertiary"
              multiline
            />
          </View>
        </View>

        <View style={styles.playerToggle}>
          <TouchableOpacity
            style={[styles.playerToggleButton, isPlayerOneTurn && styles.playerToggleButtonActive]}
            onPress={() => setIsPlayerOneTurn(true)}
          >
            <Text style={[styles.playerToggleText, isPlayerOneTurn && styles.playerToggleTextActive]}>
              Player One
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.playerToggleButton, !isPlayerOneTurn && styles.playerToggleButtonActive]}
            onPress={() => setIsPlayerOneTurn(false)}
          >
            <Text style={[styles.playerToggleText, !isPlayerOneTurn && styles.playerToggleTextActive]}>
              Player Two
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderDreamVacation();
      case 1:
        return renderDateNightPlan();
      case 2:
        return renderChatPrompts();
      case 3:
        return renderConflictStyle();
      default:
        return renderDreamVacation();
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[colors.warning, colors.orangeDark]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Week 5 of 12</Text>
          <Text style={styles.headerSubtitle}>Dream Vacation</Text>
          
          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${((data.completedSections.filter(Boolean).length / steps.length) * 100)}%` }
                ]} 
              />
            </View>
            <Text style={styles.progressText}>
              {data.completedSections.filter(Boolean).length}/{steps.length} complete
            </Text>
          </View>
        </View>
      </LinearGradient>

      {/* Navigation Steps */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.stepsContainer}>
        {steps.map((step, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.stepButton,
              currentStep === index && styles.stepButtonActive,
              data.completedSections[index] && styles.stepButtonCompleted
            ]}
            onPress={() => setCurrentStep(index)}
          >
            <LinearGradient
              colors={
                currentStep === index 
                  ? [colors.warning, colors.orangeDark]
                  : data.completedSections[index]
                    ? [colors.success, colors.greenDark]
                    : [colors.backgroundTertiary, colors.backgroundTertiary]
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.stepButtonGradient}
            >
              {step.icon}
              <Text style={[
                styles.stepButtonText,
                currentStep === index && styles.stepButtonTextActive,
                data.completedSections[index] && styles.stepButtonTextCompleted
              ]}>
                {step.title}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
            <Text style={styles.comeBackButtonText}>Come Back Later</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.continueButton}
            onPress={handleSaveAndContinue}
            disabled={currentStep >= steps.length - 1}
          >
            <LinearGradient
              colors={currentStep >= steps.length - 1 ? [colors.textTertiary, colors.textSecondary] : [colors.warning, colors.orangeDark]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.continueButtonGradient}
            >
              <Text style={styles.continueButtonText}>
                {currentStep >= steps.length - 1 ? 'All Done!' : 'Save & Continue'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 16,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.white,
    borderRadius: 4,
  },
  progressText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  stepsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  stepButton: {
    marginRight: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  stepButtonActive: {
    transform: [{ scale: 1.05 }],
  },
  stepButtonCompleted: {
    // Already styled
  },
  stepButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  stepButtonText: {
    color: colors.textSecondary,
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
  stepButtonTextActive: {
    color: colors.white,
  },
  stepButtonTextCompleted: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  adjectivesContainer: {
    marginBottom: 24,
  },
  playerSection: {
    marginBottom: 20,
  },
  playerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 12,
  },
  adjectivesInputs: {
    gap: 8,
  },
  adjectiveInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.backgroundGray,
  },
  resultsContainer: {
    backgroundColor: colors.backgroundOrange,
    padding: 16,
    borderRadius: 12,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 12,
    textAlign: 'center',
  },
  resultsRow: {
    flexDirection: 'row',
    gap: 16,
  },
  resultsColumn: {
    flex: 1,
  },
  resultsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 4,
  },
  resultsText: {
    fontSize: 14,
    color: colors.orangeDark,
    lineHeight: 20,
  },
  planningContainer: {
    gap: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.backgroundGray,
    minHeight: 48,
  },
  completeButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 16,
  },
  completeButtonGradient: {
    padding: 16,
    alignItems: 'center',
  },
  completeButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  chatContainer: {
    alignItems: 'center',
  },
  flipCard: {
    width: 280,
    height: 160,
    marginBottom: 24,
  },
  flipCardInner: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  flipCardFront: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.backgroundOrange,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.warning,
    backfaceVisibility: 'hidden',
  },
  flipCardBack: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.warning,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    position: 'absolute',
    top: 0,
    left: 0,
    backfaceVisibility: 'hidden',
  },
  flipCardText: {
    fontSize: 16,
    color: colors.orangeDark,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  flipCardQuestion: {
    fontSize: 18,
    color: colors.white,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 24,
  },
  chatInputContainer: {
    width: '100%',
    marginBottom: 24,
  },
  chatInputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  chatTextInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.backgroundGray,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  nextButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  nextButtonGradient: {
    padding: 16,
    alignItems: 'center',
  },
  nextButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  conflictStylesContainer: {
    marginBottom: 24,
  },
  conflictStylesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  conflictStyleCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  conflictStyleSelected: {
    borderColor: colors.warning,
    borderWidth: 2,
  },
  conflictStyleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  conflictStyleName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginLeft: 8,
  },
  conflictStyleDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 28,
  },
  reflectionContainer: {
    marginBottom: 24,
  },
  reflectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
    textAlign: 'center',
  },
  playerToggle: {
    flexDirection: 'row',
    backgroundColor: colors.backgroundTertiary,
    borderRadius: 12,
    padding: 4,
  },
  playerToggleButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  playerToggleButtonActive: {
    backgroundColor: colors.white,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  playerToggleText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  playerToggleTextActive: {
    color: colors.textPrimary,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 40,
  },
  comeBackButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: colors.borderMedium,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  continueButton: {
    flex: 2,
    borderRadius: 12,
    overflow: 'hidden',
  },
  continueButtonGradient: {
    padding: 16,
    alignItems: 'center',
  },
  continueButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
