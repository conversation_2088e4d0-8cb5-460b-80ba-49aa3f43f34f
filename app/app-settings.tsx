import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Settings, Bell, Moon, Sun, <PERSON>lette, Globe, Shield } from 'lucide-react-native';
import { router } from 'expo-router';
import { secureStorage } from '../utils/secureStorage';
import HamburgerMenu from '../components/HamburgerMenu';

interface AppSettings {
  darkMode: boolean;
  language: string;
  autoSave: boolean;
  dataSync: boolean;
  accessibility: boolean;
}

const APP_SETTINGS_KEY = 'app_settings';

const DEFAULT_SETTINGS: AppSettings = {
  darkMode: false,
  language: 'English',
  autoSave: true,
  dataSync: true,
  accessibility: false,
};

export default function AppSettingsScreen() {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const stored = await secureStorage.getItem<string>(APP_SETTINGS_KEY);
      if (stored) {
        setSettings({ ...DEFAULT_SETTINGS, ...JSON.parse(stored) });
      }
    } catch (error) {
      console.error('Error loading app settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: AppSettings) => {
    try {
      await secureStorage.setItem(APP_SETTINGS_KEY, JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving app settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const toggleSetting = async (key: keyof AppSettings) => {
    const newSettings = { ...settings, [key]: !settings[key] };
    await saveSettings(newSettings);
  };

  const changeLanguage = () => {
    Alert.alert(
      'Language',
      'Select your preferred language:',
      [
        { text: 'English', onPress: () => saveSettings({ ...settings, language: 'English' }) },
        { text: 'Español', onPress: () => saveSettings({ ...settings, language: 'Español' }) },
        { text: 'Français', onPress: () => saveSettings({ ...settings, language: 'Français' }) },
        { text: 'Deutsch', onPress: () => saveSettings({ ...settings, language: 'Deutsch' }) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const resetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all app settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: async () => {
            await saveSettings(DEFAULT_SETTINGS);
            Alert.alert('Success', 'Settings reset to default values.');
          }
        },
      ]
    );
  };

  const renderSettingItem = (
    icon: React.ReactNode,
    title: string,
    subtitle: string,
    value?: boolean | string,
    onPress?: () => void,
    isToggle = false
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          {icon}
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      
      {isToggle ? (
        <Switch
          value={value as boolean}
          onValueChange={onPress}
          trackColor={{ false: colors.borderLight, true: colors.lightPink }}
          thumbColor={value ? colors.lightPurple : colors.textTertiary}
        />
      ) : (
        <TouchableOpacity style={styles.settingAction} onPress={onPress}>
          <Text style={styles.settingValue}>{value}</Text>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />
      
      {/* Header */}
      <LinearGradient
        backgroundColor={colors.lightPink}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>App Settings</Text>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Appearance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          {renderSettingItem(
            <Palette size={20} color={colors.lightPink} />,
            'Dark Mode',
            'Switch between light and dark themes',
            settings.darkMode,
            () => toggleSetting('darkMode'),
            true
          )}
          {renderSettingItem(
            <Globe size={20} color={colors.lightPurple} />,
            'Language',
            'Choose your preferred language',
            settings.language,
            changeLanguage
          )}
        </View>

        {/* Data & Sync */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data & Sync</Text>
          {renderSettingItem(
            <Shield size={20} color={colors.blue} />,
            'Auto Save',
            'Automatically save your progress',
            settings.autoSave,
            () => toggleSetting('autoSave'),
            true
          )}
          {renderSettingItem(
            <Settings size={20} color={colors.success} />,
            'Data Sync',
            'Sync data across devices',
            settings.dataSync,
            () => toggleSetting('dataSync'),
            true
          )}
        </View>

        {/* Accessibility */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Accessibility</Text>
          {renderSettingItem(
            <Settings size={20} color={colors.warning} />,
            'Accessibility Features',
            'Enable enhanced accessibility options',
            settings.accessibility,
            () => toggleSetting('accessibility'),
            true
          )}
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.resetButton} onPress={resetSettings}>
            <Text style={styles.resetButtonText}>Reset to Default</Text>
          </TouchableOpacity>
        </View>

        {/* Info */}
        <View style={styles.infoCard}>
          <Settings size={24} color="#9CA3AF" />
          <Text style={styles.infoTitle}>Settings Saved</Text>
          <Text style={styles.infoText}>
            Your app settings are automatically saved and will persist across app restarts.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3E2',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    color: '#6B7280',
    marginRight: 8,
  },
  settingArrow: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  resetButton: {
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  resetButtonText: {
    color: '#DC2626',
    fontSize: 16,
    fontWeight: '600',
  },
  infoCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});
