/**
 * Index Screen - Optimized
 * 
 * Optimized version with cleaner routing logic and better error handling.
 * Demonstrates improved maintainability and user experience.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useEffect, useState, useCallback } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { Redirect } from 'expo-router';
import { useAuth } from '../contexts/AuthContext';
import { hasCompletedOnboarding } from '../utils/onboardingStorage';
import { setupGlobalErrorHandling, simpleErrorService } from '../services/simpleErrorService';
import { ErrorScreen } from '../components/ErrorHandler';
import { colors } from '../utils/colors';

// Import shared components
import { ScreenLayout } from '../components/shared';

type AppState = 'loading' | 'onboarding' | 'login' | 'main' | 'error';

export default function Index() {
  const { isAuthenticated, isLoading, isInitialized, isGuest } = useAuth();
  const [appState, setAppState] = useState<AppState>('loading');
  const [error, setError] = useState<string | null>(null);

  // Debug logging (remove in production)
  console.log('🔍 Index Debug:', {
    isAuthenticated,
    isLoading,
    isInitialized,
    isGuest,
    appState,
    error
  });

  const determineAppState = useCallback(async (): Promise<AppState> => {
    try {
      // Check if user has completed onboarding
      const onboardingCompleted = await hasCompletedOnboarding();
      
      if (!onboardingCompleted) {
        return 'onboarding';
      }

      // Check authentication status
      if (isAuthenticated || isGuest) {
        return 'main';
      }

      return 'login';
    } catch (err) {
      console.error('Error determining app state:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      
      // Report the error
      await simpleErrorService.reportError(err as Error, {
        component: 'IndexScreen',
        action: 'determineAppState',
        userId: isAuthenticated ? 'authenticated' : 'guest',
      });
      
      return 'error';
    }
  }, [isAuthenticated, isGuest]);

  const initializeApp = useCallback(async () => {
    if (!isInitialized || isLoading) {
      return;
    }

    const newState = await determineAppState();
    setAppState(newState);
  }, [isInitialized, isLoading, determineAppState]);

  const handleRetry = useCallback(() => {
    setError(null);
    setAppState('loading');
    initializeApp();
  }, [initializeApp]);

  useEffect(() => {
    // Set up global error handling
    setupGlobalErrorHandling();
  }, []);

  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  // Error state
  if (appState === 'error' && error) {
    return (
      <ErrorScreen 
        error={error}
        onRetry={handleRetry}
      />
    );
  }

  // Loading state
  if (appState === 'loading' || !isInitialized || isLoading) {
    return (
      <ScreenLayout>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </ScreenLayout>
    );
  }

  // Route based on app state
  switch (appState) {
    case 'onboarding':
      return <Redirect href="/onboarding" />;
    
    case 'login':
      return <Redirect href="/login" />;
    
    case 'main':
      return <Redirect href="/(tabs)" />;
    
    default:
      return <Redirect href="/onboarding" />;
  }
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textSecondary,
  },
});