import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Save, Camera, User, Heart, CheckCircle, Check } from 'lucide-react-native';
import { router } from 'expo-router';
import { useUserProfile } from '../hooks/useUserProfile';
import * as ImagePicker from 'expo-image-picker';
import { secureStorage } from '../utils/secureStorage';
import HamburgerMenu from '../components/HamburgerMenu';
import { colors } from '../utils/colors';

interface ProfilePicture {
  partner1?: string;
  partner2?: string;
}

const PROFILE_PICTURES_KEY = 'profile_pictures';

export default function CoupleProfileScreen() {
  const { profile, updatePartner1, updatePartner2, isLoading } = useUserProfile();
  const [partner1Name, setPartner1Name] = useState('');
  const [partner2Name, setPartner2Name] = useState('');
  const [partner1Icon, setPartner1Icon] = useState('');
  const [partner2Icon, setPartner2Icon] = useState('');
  const [profilePictures, setProfilePictures] = useState<ProfilePicture>({});
  const [isSaving, setIsSaving] = useState(false);

  // Load profile pictures on mount
  useEffect(() => {
    loadProfilePictures();
  }, []);

  // Update local state when profile is loaded
  useEffect(() => {
    if (profile && !isLoading) {
      setPartner1Name(profile.partner1.name || '');
      setPartner2Name(profile.partner2.name || '');
      setPartner1Icon(profile.partner1.icon || '');
      setPartner2Icon(profile.partner2.icon || '');
    }
  }, [profile, isLoading]);

  const loadProfilePictures = async () => {
    try {
      const stored = await secureStorage.getItem<string>(PROFILE_PICTURES_KEY);
      if (stored) {
        setProfilePictures(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading profile pictures:', error);
    }
  };

  const saveProfilePictures = async (pictures: ProfilePicture) => {
    try {
      await secureStorage.setItem(PROFILE_PICTURES_KEY, JSON.stringify(pictures));
    } catch (error) {
      console.error('Error saving profile pictures:', error);
    }
  };

  const pickImage = async (partner: 'partner1' | 'partner2') => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photo library.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPictures = { ...profilePictures, [partner]: result.assets[0].uri };
        setProfilePictures(newPictures);
        await saveProfilePictures(newPictures);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const takePhoto = async (partner: 'partner1' | 'partner2') => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your camera.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPictures = { ...profilePictures, [partner]: result.assets[0].uri };
        setProfilePictures(newPictures);
        await saveProfilePictures(newPictures);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleIconSelect = (icon: string, partner: 'partner1' | 'partner2') => {
    if (partner === 'partner1') {
      setPartner1Icon(icon);
    } else {
      setPartner2Icon(icon);
    }
  };

  const handleSave = async () => {
    if (!partner1Name.trim() || !partner2Name.trim()) {
      Alert.alert('Missing Information', 'Please enter both partner names.');
      return;
    }

    if (!partner1Icon || !partner2Icon) {
      Alert.alert('Missing Information', 'Please select icons for both partners.');
      return;
    }

    setIsSaving(true);
    try {
      await Promise.all([
        updatePartner1({ name: partner1Name.trim(), icon: partner1Icon }),
        updatePartner2({ name: partner2Name.trim(), icon: partner2Icon }),
      ]);
      
      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      console.error('Error saving profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Error', `Failed to update profile: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };

  const iconOptions = [
    { id: 'heart', symbol: '❤️', name: 'Heart' },
    { id: 'star', symbol: '⭐', name: 'Star' },
    { id: 'flower', symbol: '🌸', name: 'Flower' },
    { id: 'moon', symbol: '🌙', name: 'Moon' },
    { id: 'sun', symbol: '☀️', name: 'Sun' },
    { id: 'sparkles', symbol: '✨', name: 'Sparkles' },
    { id: 'crown', symbol: '👑', name: 'Crown' },
    { id: 'rainbow', symbol: '🌈', name: 'Rainbow' },
    { id: 'diamond', symbol: '💎', name: 'Diamond' }
  ];

  const getIconSymbol = (iconId: string): string => {
    const icon = iconOptions.find(opt => opt.id === iconId);
    return icon ? icon.symbol : '❓';
  };

  const renderIconSelector = (partner: 'partner1' | 'partner2', currentIcon: string, onSelect: (icon: string) => void) => (
    <View style={styles.iconSelector}>
      <Text style={styles.iconSelectorTitle}>Choose {partner === 'partner1' ? 'Partner 1' : 'Partner 2'} Icon:</Text>
      
      {currentIcon && (
        <View style={styles.currentIconDisplay}>
          <Text style={styles.currentIconSymbol}>{getIconSymbol(currentIcon)}</Text>
          <Text style={styles.currentIconText}>Current Icon</Text>
        </View>
      )}
      
      <View style={styles.iconGrid}>
        {iconOptions.map((icon) => (
          <TouchableOpacity
            key={icon.id}
            style={[
              styles.iconOption,
              currentIcon === icon.id && styles.iconOptionSelected
            ]}
            onPress={() => onSelect(icon.id)}
          >
            <Text style={styles.iconText}>{icon.symbol}</Text>
            {currentIcon === icon.id && (
              <View style={styles.checkmark}>
                <Check size={16} color={colors.white} />
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderProfilePicture = (partner: 'partner1' | 'partner2') => (
    <View style={styles.profilePictureSection}>
      <Text style={styles.sectionTitle}>{partner === 'partner1' ? 'Partner 1' : 'Partner 2'} Photo</Text>
      <View style={styles.pictureContainer}>
        {profilePictures[partner] ? (
          <Image source={{ uri: profilePictures[partner] }} style={styles.profilePicture} />
        ) : (
          <View style={styles.placeholderPicture}>
            <User size={48} color={colors.textTertiary} />
          </View>
        )}
        <View style={styles.pictureActions}>
          <TouchableOpacity
            style={styles.pictureButton}
            onPress={() => pickImage(partner)}
          >
            <Camera size={20} color={colors.lightPink} />
            <Text style={styles.pictureButtonText}>Choose Photo</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.pictureButton}
            onPress={() => takePhoto(partner)}
          >
            <Camera size={20} color={colors.lightPurple} />
            <Text style={styles.pictureButtonText}>Take Photo</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading profile...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <HamburgerMenu position="top-right" />
      
      <LinearGradient
        colors={colors.gradients.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Couple Profile</Text>
        <TouchableOpacity 
          style={[styles.saveButton, isSaving && styles.saveButtonDisabled]} 
          onPress={handleSave}
          disabled={isSaving}
        >
          {isSaving ? (
            <Text style={styles.saveButtonText}>Saving...</Text>
          ) : (
            <>
              <Save size={20} color={colors.white} />
              <Text style={styles.saveButtonText}>Save</Text>
            </>
          )}
        </TouchableOpacity>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Partner 1 Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Partner 1</Text>
          <TextInput
            style={styles.nameInput}
            placeholder="Enter Partner 1's name"
            value={partner1Name}
            onChangeText={setPartner1Name}
            placeholderTextColor={colors.textTertiary}
          />
          {renderIconSelector('partner1', partner1Icon, (icon) => handleIconSelect(icon, 'partner1'))}
          {renderProfilePicture('partner1')}
        </View>

        {/* Partner 2 Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Partner 2</Text>
          <TextInput
            style={styles.nameInput}
            placeholder="Enter Partner 2's name"
            value={partner2Name}
            onChangeText={setPartner2Name}
            placeholderTextColor={colors.textTertiary}
          />
          {renderIconSelector('partner2', partner2Icon, (icon) => handleIconSelect(icon, 'partner2'))}
          {renderProfilePicture('partner2')}
        </View>

        {/* Encouragement */}
        <View style={styles.encouragementCard}>
          <Heart size={32} color={colors.lightPink} />
          <Text style={styles.encouragementTitle}>Add Photos!</Text>
          <Text style={styles.encouragementText}>
            Adding photos of yourselves makes the app more personal and helps you feel more connected to your relationship journey.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.white,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: colors.white,
    fontWeight: '600',
    marginLeft: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  nameInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: colors.white,
    marginBottom: 20,
  },
  iconSelector: {
    marginBottom: 20,
  },
  iconSelectorTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 12,
  },
  currentIconDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  currentIconSymbol: {
    fontSize: 24,
    marginRight: 8,
  },
  currentIconText: {
    fontSize: 14,
    color: '#6B7280',
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  iconOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
    transform: [{ scale: 1.1 }],
  },
  iconText: {
    fontSize: 24,
  },
  checkmark: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
  },
  profilePictureSection: {
    marginTop: 10,
  },
  pictureContainer: {
    alignItems: 'center',
  },
  profilePicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  placeholderPicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  pictureActions: {
    flexDirection: 'row',
    gap: 12,
  },
  pictureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  pictureButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  encouragementCard: {
    backgroundColor: '#FDF2F8',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  encouragementTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#831843',
    marginTop: 12,
    marginBottom: 8,
  },
  encouragementText: {
    fontSize: 14,
    color: '#831843',
    textAlign: 'center',
    lineHeight: 20,
  },
});