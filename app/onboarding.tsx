/**
 * Onboarding Screen - Optimized
 * 
 * Optimized version using shared authentication components.
 * Demonstrates significant code reduction and improved maintainability.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { Heart, Users, Camera } from 'lucide-react-native';
import { colors } from '../utils/colors';
import { markOnboardingCompleted } from '../utils/onboardingStorage';
import { useUserEvents, USER_EVENTS } from '../hooks/useUserEvents';
import { useAuth } from '../contexts/AuthContext';
import { simpleErrorService } from '../services/simpleErrorService';

// Import shared auth components
import {
  AuthScreenLayout,
  OnboardingStep,
  OnboardingNavigation,
  AuthInput,
} from '../components/shared/AuthComponents';

interface OnboardingStepData {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: readonly string[];
}

const onboardingSteps: OnboardingStepData[] = [
  {
    id: 1,
    title: 'Celebrate Your Connection',
    description: 'Daily moments, thoughtful activities, and surprises designed to keep your love strong.',
    icon: <Heart size={80} color={colors.white} />,
    gradient: colors.gradients.primary,
  },
  {
    id: 2,
    title: 'Memories That Last',
    description: 'Capture laughter, milestones, and adventures — your private scrapbook grows with you.',
    icon: <Camera size={80} color={colors.white} />,
    gradient: colors.gradients.secondary,
  },
  {
    id: 3,
    title: 'A Journey Together',
    description: 'Games, prompts, and challenges that make growing together exciting — every step of the way.',
    icon: <Users size={80} color={colors.white} />,
    gradient: colors.gradients.warm,
  },
];

const TOTAL_STEPS = onboardingSteps.length + 1; // +1 for personalization step

export default function Onboarding() {
  const [currentStep, setCurrentStep] = useState(0);
  const [partnerName, setPartnerName] = useState('');
  const [showConfetti, setShowConfetti] = useState(false);
  const { logEvent } = useUserEvents();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    logEvent(USER_EVENTS.ONBOARDING_STARTED);
  }, []);

  const handleNext = () => {
    if (currentStep < TOTAL_STEPS - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleComplete = async () => {
    try {
      setShowConfetti(true);
      await markOnboardingCompleted();
      await logEvent(USER_EVENTS.ONBOARDING_COMPLETED);

      setTimeout(() => {
        router.replace('/login');
      }, 2000);
    } catch (error) {
      console.error('Error completing onboarding:', error);
      await simpleErrorService.reportError(error as Error, {
        component: 'OnboardingScreen',
        action: 'handleComplete',
        metadata: { partnerName },
      });
      router.replace('/login');
    }
  };

  const handleSkip = async () => {
    try {
      await markOnboardingCompleted();
      await logEvent(USER_EVENTS.ONBOARDING_COMPLETED);
      router.replace('/login');
    } catch (error) {
      console.error('Error skipping onboarding:', error);
      await simpleErrorService.reportError(error as Error, {
        component: 'OnboardingScreen',
        action: 'handleSkip',
        metadata: { partnerName },
      });
      router.replace('/login');
    }
  };

  const renderCurrentStep = () => {
    if (currentStep < onboardingSteps.length) {
      const step = onboardingSteps[currentStep];
      return (
        <OnboardingStep
          title={step.title}
          description={step.description}
          icon={step.icon}
          backgroundColor={step.gradient[0]}
        />
      );
    }

    // Personalization step
    return (
      <View style={styles.personalizationStep}>
        <View style={styles.personalizationContent}>
          <Text style={styles.personalizationTitle}>Let's personalize your experience</Text>
          <Text style={styles.personalizationSubtitle}>
            What should we call your partner? (This helps us make your experience more personal)
          </Text>
          
          <AuthInput
            value={partnerName}
            onChangeText={setPartnerName}
            placeholder="Enter your partner's name"
            label="Partner's Name"
            style={styles.partnerNameInput}
          />
          
          <Text style={styles.personalizationNote}>
            Don't worry, you can change this later in settings!
          </Text>
        </View>
      </View>
    );
  };

  return (
    <AuthScreenLayout
      backgroundColor={currentStep < onboardingSteps.length ? onboardingSteps[currentStep].gradient[0] : colors.primary}
      useKeyboardAvoiding={currentStep === onboardingSteps.length}
      scrollable={false}
    >
      {renderCurrentStep()}
      
      <OnboardingNavigation
        currentStep={currentStep}
        totalSteps={TOTAL_STEPS}
        onNext={handleNext}
        onSkip={handleSkip}
        onComplete={handleComplete}
        showSkip={currentStep < onboardingSteps.length}
      />
    </AuthScreenLayout>
  );
}

const styles = StyleSheet.create({
  personalizationStep: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  personalizationContent: {
    alignItems: 'center',
    width: '100%',
  },
  personalizationTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
  },
  personalizationSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 32,
  },
  partnerNameInput: {
    width: '100%',
    marginBottom: 16,
  },
  personalizationNote: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});