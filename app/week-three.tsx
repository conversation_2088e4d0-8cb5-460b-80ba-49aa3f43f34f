import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Play, Calendar, MapPin, Star, X, HelpCircle } from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useWeekThreeData } from '../hooks/useWeekThreeData';
import { colors } from '../utils/colors';

export default function WeekThreeScreen() {
  const params = useLocalSearchParams();
  const [currentStep, setCurrentStep] = useState(0);
  const [currentWYRIndex, setCurrentWYRIndex] = useState(0);
  const [currentPlayer, setCurrentPlayer] = useState<'playerOne' | 'playerTwo'>('playerOne');
  const [showWYRModal, setShowWYRModal] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [currentChatIndex, setCurrentChatIndex] = useState(0);
  const [showCuriosityModal, setShowCuriosityModal] = useState(false);
  const [currentCuriosityIndex, setCurrentCuriosityIndex] = useState(0);
  
  // Handle URL parameters for direct section navigation
  useEffect(() => {
    if (params.section) {
      const sectionIndex = parseInt(params.section as string);
      if (sectionIndex >= 0 && sectionIndex < steps.length) {
        setCurrentStep(sectionIndex);
      }
    }
  }, [params.section]);
  
  const {
    data,
    updateWouldYouRather,
    updateAlphabetDateNight,
    updateChatPrompt,
    updateCuriosityQuestion,
    updateCompletedSections,
    getFollowUpPrompts,
  } = useWeekThreeData();

  const steps = [
    { title: 'Would You Rather?', icon: <HelpCircle size={24} color="colors.white" /> },
    { title: 'Alphabet Date Night', icon: <Calendar size={24} color="colors.white" /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color="colors.white" /> },
    { title: 'Being Curious', icon: <BookOpen size={24} color="colors.white" /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Three! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleWYRQuestionPress = (index: number) => {
    setCurrentWYRIndex(index);
    setCurrentPlayer('playerOne');
    setShowWYRModal(true);
  };

  const handleWYRAnswer = (answer: string) => {
    const currentQuestion = data.wouldYouRather[currentWYRIndex];
    if (currentPlayer === 'playerOne') {
      updateWouldYouRather(currentQuestion.id, { playerOneAnswer: answer });
      setCurrentPlayer('playerTwo');
    } else {
      updateWouldYouRather(currentQuestion.id, { playerTwoAnswer: answer });
      setShowWYRModal(false);
    }
  };

  const handleWYRReason = (reason: string) => {
    const currentQuestion = data.wouldYouRather[currentWYRIndex];
    if (currentPlayer === 'playerOne') {
      updateWouldYouRather(currentQuestion.id, { playerOneReason: reason });
      setCurrentPlayer('playerTwo');
    } else {
      updateWouldYouRather(currentQuestion.id, { playerTwoReason: reason });
      setShowWYRModal(false);
    }
  };

  const handleChatPromptPress = (index: number) => {
    setCurrentChatIndex(index);
    setShowChatModal(true);
  };

  const handleChatAnswer = (player: 'playerOne' | 'playerTwo', answer: string) => {
    updateChatPrompt(currentChatIndex, { [player === 'playerOne' ? 'playerOneAnswer' : 'playerTwoAnswer']: answer });
    if (player === 'playerOne') {
      setCurrentPlayer('playerTwo');
    } else {
      setShowChatModal(false);
      setCurrentPlayer('playerOne');
    }
  };

  const handleCuriosityPress = (index: number) => {
    setCurrentCuriosityIndex(index);
    setShowCuriosityModal(true);
  };

  const renderWouldYouRather = () => {
    const petPeeveQuestions = data.wouldYouRather.filter(q => q.category === 'petPeeve');
    const fearQuestions = data.wouldYouRather.filter(q => q.category === 'fear');
    
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Would You Rather?</Text>
          <Text style={styles.sectionSubtitle}>Pet Peeve & Fear Edition</Text>
        </View>

        <View style={styles.instructions}>
          <Text style={styles.instructionText}>
            1. Use the provided questions or create your own.
          </Text>
          <Text style={styles.instructionText}>
            2. Take turns asking and answering.
          </Text>
          <Text style={styles.instructionText}>
            3. After each answer, explain why.
          </Text>
          <Text style={styles.instructionText}>
            4. Pay attention to reasoning — it reveals what matters.
          </Text>
        </View>

        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Pet Peeve Edition</Text>
          <View style={styles.questionsGrid}>
            {petPeeveQuestions.map((question, index) => (
              <TouchableOpacity
                key={question.id}
                style={[
                  styles.questionCard,
                  (question.playerOneAnswer && question.playerTwoAnswer) && styles.questionCardCompleted
                ]}
                onPress={() => handleWYRQuestionPress(data.wouldYouRather.findIndex(q => q.id === question.id))}
              >
                <Text style={styles.questionText}>{question.question}</Text>
                {(question.playerOneAnswer && question.playerTwoAnswer) && (
                  <CheckCircle size={20} color="colors.success" style={styles.completedIcon} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Fear Edition</Text>
          <View style={styles.questionsGrid}>
            {fearQuestions.map((question, index) => (
              <TouchableOpacity
                key={question.id}
                style={[
                  styles.questionCard,
                  (question.playerOneAnswer && question.playerTwoAnswer) && styles.questionCardCompleted
                ]}
                onPress={() => handleWYRQuestionPress(data.wouldYouRather.findIndex(q => q.id === question.id))}
              >
                <Text style={styles.questionText}>{question.question}</Text>
                {(question.playerOneAnswer && question.playerTwoAnswer) && (
                  <CheckCircle size={20} color="colors.success" style={styles.completedIcon} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    );
  };

  const renderAlphabetDateNight = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Alphabet Date Night</Text>
          <Text style={styles.sectionSubtitle}>Pick a letter and plan your whole date night around it!</Text>
        </View>

        <View style={styles.planCard}>
          <Text style={styles.planDescription}>
            Pick a letter and plan your whole date night around it. Foods, drinks, activities, or even outfits should start with that letter. Example: B = Burgers, Bowling, Brownies.
          </Text>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Letter Chosen</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter a letter (A-Z)"
            value={data.alphabetDateNight.letter}
            onChangeText={(text) => updateAlphabetDateNight({ letter: text.toUpperCase() })}
            maxLength={1}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When</Text>
          <TextInput
            style={styles.textInput}
            placeholder="When will this date night happen?"
            value={data.alphabetDateNight.when}
            onChangeText={(text) => updateAlphabetDateNight({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Where will this date night take place?"
            value={data.alphabetDateNight.where}
            onChangeText={(text) => updateAlphabetDateNight({ where: text })}
          />
        </View>

        <TouchableOpacity
          style={styles.completeButton}
          onPress={() => updateAlphabetDateNight({ completed: !data.alphabetDateNight.completed })}
        >
          <LinearGradient
            backgroundColor={data.alphabetDateNight.completed ? colors.success : colors.blue}
            style={styles.completeButtonGradient}
          >
            <Text style={styles.completeButtonText}>
              {data.alphabetDateNight.completed ? 'Completed!' : 'Mark as Completed'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };

  const renderChatPrompts = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Chat Prompts</Text>
          <Text style={styles.sectionSubtitle}>Deep conversations for deeper connection</Text>
        </View>

        <View style={styles.promptsContainer}>
          {data.chatPrompts.map((prompt, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.promptCard,
                (prompt.playerOneAnswer && prompt.playerTwoAnswer) && styles.promptCardCompleted
              ]}
              onPress={() => handleChatPromptPress(index)}
            >
              <Text style={styles.promptText}>{prompt.prompt}</Text>
              {(prompt.playerOneAnswer && prompt.playerTwoAnswer) && (
                <CheckCircle size={20} color="colors.success" style={styles.completedIcon} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderCuriosityToolkit = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Being Curious</Text>
          <Text style={styles.sectionSubtitle}>Curiosity strengthens emotional connection</Text>
        </View>

        <View style={styles.skillCard}>
          <Text style={styles.skillDescription}>
            Curiosity strengthens emotional connection. Ask open-ended questions and listen closely.
          </Text>
        </View>

        <View style={styles.taskSection}>
          <Text style={styles.taskTitle}>Today's Task:</Text>
          <Text style={styles.taskText}>
            Ask each other 3 open-ended questions today. Use the questions below or create your own.
          </Text>
        </View>

        <View style={styles.questionsContainer}>
          {data.curiosityQuestions.map((question, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.curiosityCard,
                (question.playerOneResponse && question.playerTwoResponse) && styles.curiosityCardCompleted
              ]}
              onPress={() => handleCuriosityPress(index)}
            >
              <Text style={styles.curiosityQuestionText}>{question.question}</Text>
              {(question.playerOneResponse && question.playerTwoResponse) && (
                <CheckCircle size={20} color="colors.success" style={styles.completedIcon} />
              )}
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.followUpSection}>
          <Text style={styles.followUpTitle}>Follow-up Prompts:</Text>
          {getFollowUpPrompts().map((prompt, index) => (
            <Text key={index} style={styles.followUpText}>• {prompt}</Text>
          ))}
        </View>
      </View>
    );
  };

  const renderProgressBar = () => {
    const completedCount = data.completedSections.filter(Boolean).length;
    const progress = (completedCount / steps.length) * 100;
    
    return (
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>Week 3 of 12</Text>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressCount}>{completedCount}/{steps.length} complete</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        backgroundColor={colors.blue}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="colors.white" />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Week Three</Text>
          <Text style={styles.headerSubtitle}>Would You Rather?</Text>
        </View>
      </LinearGradient>

      {/* Progress Bar */}
      {renderProgressBar()}

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Step Navigation */}
        <View style={styles.stepNavigation}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.stepButton,
                currentStep === index && styles.stepButtonActive,
                data.completedSections[index] && styles.stepButtonCompleted
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={20} color="colors.white" />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.stepButtonText,
                currentStep === index && styles.stepButtonTextActive,
                data.completedSections[index] && styles.stepButtonTextCompleted
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Current Step Content */}
        {currentStep === 0 && renderWouldYouRather()}
        {currentStep === 1 && renderAlphabetDateNight()}
        {currentStep === 2 && renderChatPrompts()}
        {currentStep === 3 && renderCuriosityToolkit()}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
            <Text style={styles.comeBackButtonText}>Come Back Later</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.saveButton} onPress={handleSaveAndContinue}>
            <LinearGradient
              backgroundColor={colors.blue}
              style={styles.saveButtonGradient}
            >
              <Save size={20} color="colors.white" />
              <Text style={styles.saveButtonText}>
                {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Would You Rather Modal */}
      <Modal visible={showWYRModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentPlayer === 'playerOne' ? 'Player One' : 'Player Two'}
              </Text>
              <TouchableOpacity onPress={() => setShowWYRModal(false)}>
                <X size={24} color="colors.textSecondary" />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.modalQuestion}>
              {data.wouldYouRather[currentWYRIndex]?.question}
            </Text>
            
            <Text style={styles.modalSubtitle}>
              {currentPlayer === 'playerOne' ? 'What\'s your answer?' : 'What\'s your answer?'}
            </Text>
            
            <TextInput
              style={styles.modalInput}
              placeholder="Type your answer..."
              value={currentPlayer === 'playerOne' 
                ? data.wouldYouRather[currentWYRIndex]?.playerOneAnswer 
                : data.wouldYouRather[currentWYRIndex]?.playerTwoAnswer
              }
              onChangeText={(text) => {
                if (currentPlayer === 'playerOne') {
                  updateWouldYouRather(data.wouldYouRather[currentWYRIndex].id, { playerOneAnswer: text });
                } else {
                  updateWouldYouRather(data.wouldYouRather[currentWYRIndex].id, { playerTwoAnswer: text });
                }
              }}
            />
            
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                if (currentPlayer === 'playerOne') {
                  setCurrentPlayer('playerTwo');
                } else {
                  setShowWYRModal(false);
                  setCurrentPlayer('playerOne');
                }
              }}
            >
              <Text style={styles.modalButtonText}>
                {currentPlayer === 'playerOne' ? 'Next Player' : 'Done'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Chat Prompts Modal */}
      <Modal visible={showChatModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentPlayer === 'playerOne' ? 'Player One' : 'Player Two'}
              </Text>
              <TouchableOpacity onPress={() => setShowChatModal(false)}>
                <X size={24} color="colors.textSecondary" />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.modalQuestion}>
              {data.chatPrompts[currentChatIndex]?.prompt}
            </Text>
            
            <Text style={styles.modalSubtitle}>
              {currentPlayer === 'playerOne' ? 'What\'s your answer?' : 'What\'s your answer?'}
            </Text>
            
            <TextInput
              style={styles.modalInput}
              placeholder="Type your answer..."
              value={currentPlayer === 'playerOne' 
                ? data.chatPrompts[currentChatIndex]?.playerOneAnswer 
                : data.chatPrompts[currentChatIndex]?.playerTwoAnswer
              }
              onChangeText={(text) => {
                if (currentPlayer === 'playerOne') {
                  updateChatPrompt(currentChatIndex, { playerOneAnswer: text });
                } else {
                  updateChatPrompt(currentChatIndex, { playerTwoAnswer: text });
                }
              }}
            />
            
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                if (currentPlayer === 'playerOne') {
                  setCurrentPlayer('playerTwo');
                } else {
                  setShowChatModal(false);
                  setCurrentPlayer('playerOne');
                }
              }}
            >
              <Text style={styles.modalButtonText}>
                {currentPlayer === 'playerOne' ? 'Next Player' : 'Done'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Curiosity Modal */}
      <Modal visible={showCuriosityModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Curiosity Question</Text>
              <TouchableOpacity onPress={() => setShowCuriosityModal(false)}>
                <X size={24} color="colors.textSecondary" />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.modalQuestion}>
              {data.curiosityQuestions[currentCuriosityIndex]?.question}
            </Text>
            
            <View style={styles.curiosityInputs}>
              <Text style={styles.modalSubtitle}>Player One's Response:</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="Type your response..."
                value={data.curiosityQuestions[currentCuriosityIndex]?.playerOneResponse}
                onChangeText={(text) => updateCuriosityQuestion(currentCuriosityIndex, { playerOneResponse: text })}
              />
              
              <Text style={styles.modalSubtitle}>Player Two's Response:</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="Type your response..."
                value={data.curiosityQuestions[currentCuriosityIndex]?.playerTwoResponse}
                onChangeText={(text) => updateCuriosityQuestion(currentCuriosityIndex, { playerTwoResponse: text })}
              />
              
              <Text style={styles.modalSubtitle}>Follow-up Used:</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="What follow-up did you use?"
                value={data.curiosityQuestions[currentCuriosityIndex]?.followUpUsed}
                onChangeText={(text) => updateCuriosityQuestion(currentCuriosityIndex, { followUpUsed: text })}
              />
            </View>
            
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => setShowCuriosityModal(false)}
            >
              <Text style={styles.modalButtonText}>Done</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'colors.backgroundGray',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 18,
    color: colors.white,
    opacity: 0.9,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.borderLight,
    borderRadius: 4,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'colors.blue',
    borderRadius: 4,
  },
  progressCount: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  stepNavigation: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  stepButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 12,
    backgroundColor: 'colors.backgroundTertiary',
  },
  stepButtonActive: {
    backgroundColor: 'colors.blue',
  },
  stepButtonCompleted: {
    backgroundColor: colors.success,
  },
  stepButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    marginTop: 5,
    textAlign: 'center',
  },
  stepButtonTextActive: {
    color: colors.white,
  },
  stepButtonTextCompleted: {
    color: colors.white,
  },
  sectionContainer: {
    backgroundColor: colors.white,
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  instructions: {
    backgroundColor: colors.backgroundPink,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.blueDark,
    marginBottom: 5,
  },
  categorySection: {
    marginBottom: 25,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 15,
  },
  questionsGrid: {
    gap: 12,
  },
  questionCard: {
    backgroundColor: 'colors.backgroundGray',
    padding: 15,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderLight,
    position: 'relative',
  },
  questionCardCompleted: {
    borderColor: colors.success,
    backgroundColor: colors.backgroundGray,
  },
  questionText: {
    fontSize: 14,
    color: colors.textPrimary,
    lineHeight: 20,
  },
  completedIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  planCard: {
    backgroundColor: colors.backgroundPink,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  planDescription: {
    fontSize: 14,
    color: colors.blueDark,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
  },
  completeButton: {
    marginTop: 10,
  },
  completeButtonGradient: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  promptsContainer: {
    gap: 15,
  },
  promptCard: {
    backgroundColor: 'colors.backgroundGray',
    padding: 15,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderLight,
    position: 'relative',
  },
  promptCardCompleted: {
    borderColor: colors.success,
    backgroundColor: colors.backgroundGray,
  },
  promptText: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 22,
  },
  skillCard: {
    backgroundColor: colors.backgroundPink,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  skillDescription: {
    fontSize: 14,
    color: colors.blueDark,
    lineHeight: 20,
  },
  taskSection: {
    marginBottom: 20,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  taskText: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  questionsContainer: {
    gap: 15,
    marginBottom: 20,
  },
  curiosityCard: {
    backgroundColor: 'colors.backgroundGray',
    padding: 15,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderLight,
    position: 'relative',
  },
  curiosityCardCompleted: {
    borderColor: colors.success,
    backgroundColor: colors.backgroundGray,
  },
  curiosityQuestionText: {
    fontSize: 14,
    color: colors.textPrimary,
    lineHeight: 20,
  },
  followUpSection: {
    backgroundColor: colors.backgroundOrange,
    padding: 15,
    borderRadius: 12,
  },
  followUpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 10,
  },
  followUpText: {
    fontSize: 14,
    color: colors.orangeDark,
    marginBottom: 5,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 15,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  saveButton: {
    flex: 2,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    margin: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  modalQuestion: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 22,
    marginBottom: 15,
  },
  modalSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
    marginBottom: 20,
  },
  modalButton: {
    backgroundColor: 'colors.blue',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  curiosityInputs: {
    marginBottom: 20,
  },
});
