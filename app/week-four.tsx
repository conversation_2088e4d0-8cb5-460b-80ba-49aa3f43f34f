import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Save, CheckCircle, Heart, Users, MessageCircle, BookOpen, Play, Calendar, MapPin, Star, X, Music, Film, Trash2, Plus } from 'lucide-react-native';
import { router } from 'expo-router';
import { useWeekFourData } from '../hooks/useWeekFourData';
import { colors } from '../utils/colors';

export default function WeekFourScreen() {
  const [currentStep, setCurrentStep] = useState(0);
  const [showAddSongModal, setShowAddSongModal] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [currentChatIndex, setCurrentChatIndex] = useState(0);
  const [currentPlayer, setCurrentPlayer] = useState<'playerOne' | 'playerTwo'>('playerOne');
  const [newSong, setNewSong] = useState({ title: '', artist: '', note: '' });
  
  const {
    data,
    addPlaylistSong,
    updatePlaylistSong,
    removePlaylistSong,
    updateMovieThemeNight,
    updateChatPrompt,
    updateEmotionalRegulation,
    updateCompletedSections,
    getInspirationPrompts,
  } = useWeekFourData();

  const steps = [
    { title: 'Create a Playlist', icon: <Music size={24} color={colors.white} /> },
    { title: 'Movie Theme Night', icon: <Film size={24} color={colors.white} /> },
    { title: 'Chat Prompts', icon: <MessageCircle size={24} color={colors.white} /> },
    { title: 'Emotional Regulation', icon: <BookOpen size={24} color={colors.white} /> },
  ];

  const handleSaveAndContinue = () => {
    if (currentStep < steps.length - 1) {
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      setCurrentStep(currentStep + 1);
    } else {
      // All steps completed
      const newSections = [...data.completedSections];
      newSections[currentStep] = true;
      updateCompletedSections(newSections);
      Alert.alert('Congratulations!', 'You\'ve completed Week Four! All your responses have been saved to your scrapbook.');
    }
  };

  const handleComeBackLater = () => {
    router.back();
  };

  const handleAddSong = () => {
    if (newSong.title.trim() && newSong.artist.trim()) {
      addPlaylistSong(newSong);
      setNewSong({ title: '', artist: '', note: '' });
      setShowAddSongModal(false);
    } else {
      Alert.alert('Missing Information', 'Please enter both song title and artist.');
    }
  };

  const handleChatPromptPress = (index: number) => {
    setCurrentChatIndex(index);
    setCurrentPlayer('playerOne');
    setShowChatModal(true);
  };

  const handleChatAnswer = (player: 'playerOne' | 'playerTwo', answer: string) => {
    updateChatPrompt(currentChatIndex, { [player === 'playerOne' ? 'playerOneAnswer' : 'playerTwoAnswer']: answer });
    if (player === 'playerOne') {
      setCurrentPlayer('playerTwo');
    } else {
      setShowChatModal(false);
      setCurrentPlayer('playerOne');
    }
  };

  const renderPlaylistBuilder = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Create a Playlist</Text>
          <Text style={styles.sectionSubtitle}>If your relationship had a soundtrack, what would it sound like?</Text>
        </View>

        <View style={styles.instructions}>
          <Text style={styles.instructionText}>
            If your relationship had a soundtrack, what would it sound like? Pick 6–10 songs that capture your journey. Add the title, artist, and an optional note.
          </Text>
        </View>

        <View style={styles.inspirationSection}>
          <Text style={styles.inspirationTitle}>Need inspiration? Try these:</Text>
          {getInspirationPrompts().map((prompt, index) => (
            <Text key={index} style={styles.inspirationPrompt}>• {prompt}</Text>
          ))}
        </View>

        <TouchableOpacity
          style={styles.addSongButton}
          onPress={() => setShowAddSongModal(true)}
        >
          <LinearGradient
            colors={[colors.success, colors.greenDark]}
            style={styles.addSongButtonGradient}
          >
            <Plus size={20} color={colors.white} />
            <Text style={styles.addSongButtonText}>Add Song</Text>
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.playlistContainer}>
          {data.playlistSongs.length === 0 ? (
            <View style={styles.emptyPlaylist}>
              <Music size={48} color={colors.borderMedium} />
              <Text style={styles.emptyPlaylistText}>Your playlist is empty</Text>
              <Text style={styles.emptyPlaylistSubtext}>Start adding songs to create your relationship soundtrack!</Text>
            </View>
          ) : (
            data.playlistSongs.map((song, index) => (
              <View key={song.id} style={styles.songCard}>
                <View style={styles.songInfo}>
                  <Text style={styles.songTitle}>{song.title}</Text>
                  <Text style={styles.songArtist}>{song.artist}</Text>
                  {song.note && (
                    <Text style={styles.songNote}>{song.note}</Text>
                  )}
                </View>
                <TouchableOpacity
                  style={styles.removeSongButton}
                  onPress={() => removePlaylistSong(song.id)}
                >
                  <Trash2 size={16} color={colors.error} />
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>

        {data.playlistSongs.length > 0 && (
          <View style={styles.playlistStats}>
            <Text style={styles.playlistStatsText}>
              {data.playlistSongs.length} song{data.playlistSongs.length !== 1 ? 's' : ''} in your playlist
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderMovieThemeNight = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Movie Theme Night</Text>
          <Text style={styles.sectionSubtitle}>Pick a theme and create the perfect movie night!</Text>
        </View>

        <View style={styles.planCard}>
          <Text style={styles.planDescription}>
            Pick a fun theme (80s, rom-coms, childhood favorites, cheesy classics, etc.). Add themed snacks, cozy blankets, or matching outfits.
          </Text>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Theme Chosen</Text>
          <TextInput
            style={styles.textInput}
            placeholder="e.g., 80s throwbacks, rom-coms, cheesy favorites"
            value={data.movieThemeNight.theme}
            onChangeText={(text) => updateMovieThemeNight({ theme: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>When</Text>
          <TextInput
            style={styles.textInput}
            placeholder="When will this movie night happen?"
            value={data.movieThemeNight.when}
            onChangeText={(text) => updateMovieThemeNight({ when: text })}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>Where</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Where will this movie night take place?"
            value={data.movieThemeNight.where}
            onChangeText={(text) => updateMovieThemeNight({ where: text })}
          />
        </View>

        <TouchableOpacity
          style={styles.completeButton}
          onPress={() => updateMovieThemeNight({ completed: !data.movieThemeNight.completed })}
        >
          <LinearGradient
            colors={[colors.success, colors.greenDark]}
            style={styles.completeButtonGradient}
          >
            <Text style={styles.completeButtonText}>
              {data.movieThemeNight.completed ? 'Completed!' : 'Mark as Completed'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  };

  const renderChatPrompts = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Chat Prompts</Text>
          <Text style={styles.sectionSubtitle}>Deep conversations for deeper connection</Text>
        </View>

        <View style={styles.promptsContainer}>
          {data.chatPrompts.map((prompt, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.promptCard,
                (prompt.playerOneAnswer && prompt.playerTwoAnswer) && styles.promptCardCompleted
              ]}
              onPress={() => handleChatPromptPress(index)}
            >
              <Text style={styles.promptText}>{prompt.prompt}</Text>
              {(prompt.playerOneAnswer && prompt.playerTwoAnswer) && (
                <CheckCircle size={20} color={colors.success} style={styles.completedIcon} />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderEmotionalRegulation = () => {
    return (
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Emotional Regulation</Text>
          <Text style={styles.sectionSubtitle}>Pause, Share, Connect</Text>
        </View>

        <View style={styles.skillCard}>
          <Text style={styles.skillDescription}>
            Expert spotlight: Dr. Sue Johnson, Emotionally Focused Therapy
          </Text>
        </View>

        <View style={styles.stepsSection}>
          <Text style={styles.stepsTitle}>Steps to practice together:</Text>
          
          <View style={styles.stepCard}>
            <Text style={styles.stepNumber}>1. Pause</Text>
            <Text style={styles.stepDescription}>
              Slow things down with a gentle check-in.
            </Text>
            <Text style={styles.stepExample}>
              Example: "Can I check in with you for a sec?"
            </Text>
          </View>

          <View style={styles.stepCard}>
            <Text style={styles.stepNumber}>2. Share</Text>
            <Text style={styles.stepDescription}>
              Use an "I" statement.
            </Text>
            <Text style={styles.stepExample}>
              Example: "I feel a bit off when we aren't talking after work. It makes me wonder if something is wrong."
            </Text>
          </View>

          <View style={styles.stepCard}>
            <Text style={styles.stepNumber}>3. Connect</Text>
            <Text style={styles.stepDescription}>
              Invite your partner with care.
            </Text>
            <Text style={styles.stepExample}>
              Example: "Can we talk about it for a minute?"
            </Text>
          </View>
        </View>

        <View style={styles.practiceSection}>
          <Text style={styles.practiceTitle}>Practice Together:</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Pause sentence:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Type your pause statement..."
              value={data.emotionalRegulation.pauseStatement}
              onChangeText={(text) => updateEmotionalRegulation({ pauseStatement: text })}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>"I" statement:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Type your I statement..."
              value={data.emotionalRegulation.iStatement}
              onChangeText={(text) => updateEmotionalRegulation({ iStatement: text })}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Connection invitation:</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Type your connecting invitation..."
              value={data.emotionalRegulation.connectInvitation}
              onChangeText={(text) => updateEmotionalRegulation({ connectInvitation: text })}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderProgressBar = () => {
    const completedCount = data.completedSections.filter(Boolean).length;
    const progress = (completedCount / steps.length) * 100;
    
    return (
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>Week 4 of 12</Text>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${progress}%` }]} />
        </View>
        <Text style={styles.progressCount}>{completedCount}/{steps.length} complete</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={[colors.success, colors.greenDark]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={colors.white} />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Week Four</Text>
          <Text style={styles.headerSubtitle}>Create a Playlist</Text>
        </View>
      </LinearGradient>

      {/* Progress Bar */}
      {renderProgressBar()}

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Step Navigation */}
        <View style={styles.stepNavigation}>
          {steps.map((step, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.stepButton,
                currentStep === index && styles.stepButtonActive,
                data.completedSections[index] && styles.stepButtonCompleted
              ]}
              onPress={() => setCurrentStep(index)}
            >
              {data.completedSections[index] ? (
                <CheckCircle size={20} color={colors.white} />
              ) : (
                step.icon
              )}
              <Text style={[
                styles.stepButtonText,
                currentStep === index && styles.stepButtonTextActive,
                data.completedSections[index] && styles.stepButtonTextCompleted
              ]}>
                {step.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Current Step Content */}
        {currentStep === 0 && renderPlaylistBuilder()}
        {currentStep === 1 && renderMovieThemeNight()}
        {currentStep === 2 && renderChatPrompts()}
        {currentStep === 3 && renderEmotionalRegulation()}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.comeBackButton} onPress={handleComeBackLater}>
            <Text style={styles.comeBackButtonText}>Come Back Later</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.saveButton} onPress={handleSaveAndContinue}>
            <LinearGradient
              colors={[colors.success, colors.greenDark]}
              style={styles.saveButtonGradient}
            >
              <Save size={20} color={colors.white} />
              <Text style={styles.saveButtonText}>
                {currentStep < steps.length - 1 ? 'Save & Continue' : 'Complete Week'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Add Song Modal */}
      <Modal visible={showAddSongModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add Song to Playlist</Text>
              <TouchableOpacity onPress={() => setShowAddSongModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Song Title *</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="Enter song title..."
                value={newSong.title}
                onChangeText={(text) => setNewSong({ ...newSong, title: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Artist *</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="Enter artist name..."
                value={newSong.artist}
                onChangeText={(text) => setNewSong({ ...newSong, artist: text })}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Why this song fits us (optional)</Text>
              <TextInput
                style={styles.modalInput}
                placeholder="Why does this song represent your relationship?"
                value={newSong.note}
                onChangeText={(text) => setNewSong({ ...newSong, note: text })}
                multiline
              />
            </View>
            
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleAddSong}
            >
              <Text style={styles.modalButtonText}>Add to Playlist</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Chat Prompts Modal */}
      <Modal visible={showChatModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentPlayer === 'playerOne' ? 'Player One' : 'Player Two'}
              </Text>
              <TouchableOpacity onPress={() => setShowChatModal(false)}>
                <X size={24} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.modalQuestion}>
              {data.chatPrompts[currentChatIndex]?.prompt}
            </Text>
            
            <Text style={styles.modalSubtitle}>
              {currentPlayer === 'playerOne' ? 'What\'s your answer?' : 'What\'s your answer?'}
            </Text>
            
            <TextInput
              style={styles.modalInput}
              placeholder="Type your answer..."
              value={currentPlayer === 'playerOne' 
                ? data.chatPrompts[currentChatIndex]?.playerOneAnswer 
                : data.chatPrompts[currentChatIndex]?.playerTwoAnswer
              }
              onChangeText={(text) => {
                if (currentPlayer === 'playerOne') {
                  updateChatPrompt(currentChatIndex, { playerOneAnswer: text });
                } else {
                  updateChatPrompt(currentChatIndex, { playerTwoAnswer: text });
                }
              }}
            />
            
            <TouchableOpacity
              style={styles.modalButton}
              onPress={() => {
                if (currentPlayer === 'playerOne') {
                  setCurrentPlayer('playerTwo');
                } else {
                  setShowChatModal(false);
                  setCurrentPlayer('playerOne');
                }
              }}
            >
              <Text style={styles.modalButtonText}>
                {currentPlayer === 'playerOne' ? 'Next Player' : 'Done'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundGray,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1,
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 18,
    color: colors.white,
    opacity: 0.9,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 10,
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.borderLight,
    borderRadius: 4,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.success,
    borderRadius: 4,
  },
  progressCount: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  stepNavigation: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  stepButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 12,
    backgroundColor: colors.backgroundTertiary,
  },
  stepButtonActive: {
    backgroundColor: colors.success,
  },
  stepButtonCompleted: {
    backgroundColor: colors.success,
  },
  stepButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    marginTop: 5,
    textAlign: 'center',
  },
  stepButtonTextActive: {
    color: colors.white,
  },
  stepButtonTextCompleted: {
    color: colors.white,
  },
  sectionContainer: {
    backgroundColor: colors.white,
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  instructions: {
    backgroundColor: colors.backgroundOrange,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    color: colors.orangeDark,
    lineHeight: 20,
  },
  inspirationSection: {
    backgroundColor: colors.backgroundPink,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  inspirationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.blueDark,
    marginBottom: 10,
  },
  inspirationPrompt: {
    fontSize: 14,
    color: colors.blueDark,
    marginBottom: 5,
    lineHeight: 18,
  },
  addSongButton: {
    marginBottom: 20,
  },
  addSongButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  addSongButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  playlistContainer: {
    gap: 12,
  },
  emptyPlaylist: {
    alignItems: 'center',
    padding: 40,
  },
  emptyPlaylistText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textSecondary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyPlaylistSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
    lineHeight: 20,
  },
  songCard: {
    backgroundColor: colors.backgroundGray,
    padding: 15,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
    flexDirection: 'row',
    alignItems: 'center',
  },
  songInfo: {
    flex: 1,
  },
  songTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  songArtist: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  songNote: {
    fontSize: 12,
    color: colors.textTertiary,
    fontStyle: 'italic',
  },
  removeSongButton: {
    padding: 8,
  },
  playlistStats: {
    alignItems: 'center',
    marginTop: 20,
    padding: 15,
    backgroundColor: colors.backgroundGray,
    borderRadius: 12,
  },
  playlistStatsText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.greenDark,
  },
  planCard: {
    backgroundColor: colors.backgroundOrange,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  planDescription: {
    fontSize: 14,
    color: colors.orangeDark,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
  },
  completeButton: {
    marginTop: 10,
  },
  completeButtonGradient: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  promptsContainer: {
    gap: 15,
  },
  promptCard: {
    backgroundColor: colors.backgroundGray,
    padding: 15,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderLight,
    position: 'relative',
  },
  promptCardCompleted: {
    borderColor: colors.success,
    backgroundColor: colors.backgroundGray,
  },
  promptText: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 22,
  },
  completedIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  skillCard: {
    backgroundColor: colors.backgroundPink,
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  skillDescription: {
    fontSize: 14,
    color: colors.blueDark,
    lineHeight: 20,
  },
  stepsSection: {
    marginBottom: 20,
  },
  stepsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 15,
  },
  stepCard: {
    backgroundColor: colors.backgroundGray,
    padding: 15,
    borderRadius: 12,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: colors.success,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: colors.success,
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 14,
    color: colors.textPrimary,
    marginBottom: 8,
    lineHeight: 20,
  },
  stepExample: {
    fontSize: 12,
    color: colors.textSecondary,
    fontStyle: 'italic',
    lineHeight: 18,
  },
  practiceSection: {
    backgroundColor: colors.backgroundOrange,
    padding: 15,
    borderRadius: 12,
  },
  practiceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.orangeDark,
    marginBottom: 15,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 15,
  },
  comeBackButton: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.borderMedium,
    alignItems: 'center',
  },
  comeBackButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  saveButton: {
    flex: 2,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    margin: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  modalQuestion: {
    fontSize: 16,
    color: colors.textPrimary,
    lineHeight: 22,
    marginBottom: 15,
  },
  modalSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
    marginBottom: 8,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: colors.borderMedium,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
    marginBottom: 20,
  },
  modalButton: {
    backgroundColor: colors.success,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
