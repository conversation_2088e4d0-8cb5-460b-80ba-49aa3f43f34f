/**
 * Home Screen - Clean Version
 * 
 * Clean, maintainable version of the home screen using shared components and custom hooks.
 * This demonstrates the final refactored approach with separated concerns.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React from 'react';
import { Text, StyleSheet, View } from 'react-native';
import { Heart, Calendar, Star, Play, Plus } from 'lucide-react-native';
import { router } from 'expo-router';
import { useUserProfile } from '../../hooks/useUserProfile';
import { usePointsSystemSupabase } from '../../hooks/usePointsSystemSupabase';
import { colors } from '../../utils/colors';
import HamburgerMenu from '../../components/HamburgerMenu';
import { useAuth } from '../../contexts/AuthContext';
import { useHomeScreen } from '../../hooks/useHomeScreen';

// Import shared components
import {
  ScreenLayout,
  HeaderLayout,
  ContentLayout,
  SectionLayout,
  FlexRow,
  FlexColumn,
  Spacer,
  GradientCard,
  StatCard,
  GradientButton,
  ProgressBar,
  WeekSelector,
  EnhancedModal,
  EnhancedInput,
  ActivityCard,
} from '../../components/shared';

export default function HomeScreen() {
  const { isGuest } = useAuth();
  const { getCoupleNames } = useUserProfile();
  const { totalPoints } = usePointsSystemSupabase();
  
  const {
    currentWeek,
    setCurrentWeek,
    showAddGoalModal,
    newGoalText,
    setNewGoalText,
    getCurrentWeekActivities,
    getWeekProgress,
    getAllGoals,
    getGreeting,
    getNextMilestone,
    addCustomGoal,
    openAddGoalModal,
    closeAddGoalModal,
  } = useHomeScreen();

  // Guest mode component
  if (isGuest) {
    return (
      <ScreenLayout backgroundColor={colors.primary}>
        <HeaderLayout gradientColors={[colors.primary, colors.secondary]} height={300}>
          <FlexColumn alignItems="center" gap={16}>
          <Text style={styles.guestTitle}>Welcome, Guest!</Text>
          <Text style={styles.guestSubtitle}>
            You're exploring Everlasting Us in guest mode. Create an account to unlock all features and save your progress.
          </Text>
            <GradientButton
              title="Create Account"
              onPress={() => router.push('/auth')}
              gradientColors={[colors.white, colors.backgroundSecondary]}
            style={styles.guestButton}
            />
          </FlexColumn>
        </HeaderLayout>
      </ScreenLayout>
    );
  }

  const weekProgress = getWeekProgress(currentWeek);
  const milestone = getNextMilestone();
  const currentStreak = 0; // This would come from a service

  return (
    <ScreenLayout>
      <HamburgerMenu position="top-right" />
      
      {/* Header Section */}
      <HeaderLayout gradientColors={[...colors.gradients.header]}>
        <FlexColumn alignItems="center" gap={8}>
        <Text style={styles.greeting}>{getGreeting()}</Text>
        <Text style={styles.coupleNames}>{getCoupleNames()}</Text>
        <Text style={styles.tagline}>Your haven for love, laughter, and growth</Text>
        </FlexColumn>
      </HeaderLayout>

      <ContentLayout>
        {/* Progress Card */}
        <SectionLayout title={`Week ${currentWeek} Progress`} margin={20}>
          <FlexRow justifyContent="space-between" alignItems="center" gap={16}>
          <Text style={styles.progressPercent}>{weekProgress}%</Text>
            <View style={{ flex: 1 }}>
              <WeekSelector
                currentWeek={currentWeek}
                totalWeeks={12}
                onWeekSelect={setCurrentWeek}
              />
              <ProgressBar
                current={weekProgress}
                total={100}
                showPercentage={false}
              />
        <Text style={styles.progressSubtext}>
          {weekProgress === 100 ? 'Amazing! Week completed!' : 
           weekProgress === 0 ? 'Ready to start this week?' : 
           `Keep going! You're ${100 - weekProgress}% away from completing this week.`}
        </Text>
      </View>
          </FlexRow>
        </SectionLayout>

        {/* Stats Row */}
        <SectionLayout margin={20}>
          <FlexRow justifyContent="space-between" gap={8}>
            <StatCard
              number={totalPoints}
              label="Points"
              icon={<Star size={20} color={colors.warning} />}
              iconColor={colors.warning}
            />
            <StatCard
              number={currentStreak}
              label="Day Streak"
              icon={<Heart size={20} color={colors.primary} />}
              iconColor={colors.primary}
            />
            <StatCard
              number={0}
              label="Weeks Done"
              icon={<Calendar size={20} color={colors.blue} />}
              iconColor={colors.blue}
            />
          </FlexRow>
        </SectionLayout>

        {/* Quick Access Card */}
        <SectionLayout margin={20}>
          <GradientCard
            gradientColors={[...colors.gradients.warm]}
            pressable={true}
            onPress={() => router.push('/(tabs)/date-night')}
          >
            <FlexRow justifyContent="space-between" alignItems="center" gap={16}>
              <FlexColumn gap={4}>
              <Text style={styles.quickAccessTitle}>💕 Date Night Ideas</Text>
              <Text style={styles.quickAccessSubtitle}>Plan the perfect evening with activities + meals</Text>
              </FlexColumn>
              <GradientButton
                title="Try It"
                onPress={() => router.push('/(tabs)/date-night')}
                backgroundColor={colors.whiteOverlayMedium}
                size="small"
              style={styles.quickAccessButton}
              />
            </FlexRow>
          </GradientCard>
        </SectionLayout>

        {/* Goals Section */}
        <SectionLayout 
          title={`Week ${currentWeek} Goals`} 
          margin={20}
        >
          <FlexRow justifyContent="space-between" alignItems="center" gap={16}>
            <View style={{ flex: 1 }} />
            <GradientButton
              title=""
              onPress={openAddGoalModal}
              gradientColors={[colors.secondary, colors.secondaryLight]}
              size="small"
              icon={<Plus size={20} color={colors.white} />}
            style={styles.addGoalButton}
            />
          </FlexRow>
          
          <FlexColumn gap={12}>
            {getAllGoals().map((goal) => (
              <ActivityCard
                key={goal.id}
                title={goal.title}
                category={goal.category}
                completed={goal.completed}
                points={goal.isModuleGoal ? 25 : undefined}
              onPress={goal.action}
              />
            ))}
          </FlexColumn>
        
        {getAllGoals().length === 0 && (
          <Text style={styles.noGoalsText}>No goals set for this week. Add some to get started!</Text>
        )}
        </SectionLayout>

        {/* Progress Overview */}
        <SectionLayout title="Progress Overview" margin={20}>
          <ProgressBar
            current={0}
            total={12}
            showPercentage={false}
          />
          <Text style={styles.progressText}>0 of 12 weeks completed</Text>
        </SectionLayout>

        {/* Quick Links */}
        <SectionLayout margin={20}>
          <FlexColumn gap={16}>
            <GradientCard
              gradientColors={[...colors.gradients.warm]}
              pressable={true}
        onPress={() => router.push('/(tabs)/date-night')}
      >
              <FlexColumn alignItems="center" gap={8}>
            <Text style={styles.quickLinkTitle}>💕 Date Night</Text>
            <Text style={styles.quickLinkSubtitle}>Plan romantic evenings and discover new date ideas</Text>
              </FlexColumn>
            </GradientCard>

            <GradientCard
              gradientColors={[...colors.gradients.primary]}
              pressable={true}
        onPress={() => router.push('/(tabs)/activities')}
      >
              <FlexColumn alignItems="center" gap={8}>
            <Text style={styles.quickLinkTitle}>🎮 Activities Hub</Text>
            <Text style={styles.quickLinkSubtitle}>Play interactive games and challenges together</Text>
              </FlexColumn>
            </GradientCard>

            <GradientCard
              gradientColors={[...colors.gradients.secondary]}
              pressable={true}
        onPress={() => router.push('/(tabs)/modules')}
      >
              <FlexColumn alignItems="center" gap={8}>
            <Text style={styles.quickLinkTitle}>📚 12-Week Program</Text>
            <Text style={styles.quickLinkSubtitle}>Continue your relationship journey with guided activities</Text>
              </FlexColumn>
            </GradientCard>
          </FlexColumn>
        </SectionLayout>

        {/* Summary Card */}
        <SectionLayout margin={20}>
          <GradientCard backgroundColor={colors.backgroundOrange}>
            <FlexColumn alignItems="center" gap={16}>
          <Text style={styles.summaryTitle}>Keep Going!</Text>
              <Text style={styles.summaryText}>{milestone.text}</Text>
              <GradientButton
                title="Continue Journey"
                onPress={() => {}}
                backgroundColor={colors.lightPurple}
                size="small"
              />
            </FlexColumn>
          </GradientCard>
        </SectionLayout>

        {/* Quick Actions */}
        <SectionLayout margin={20}>
          <FlexColumn gap={12}>
            <GradientButton
              title="Start Week Activity"
              onPress={() => {
          const activities = getCurrentWeekActivities();
          if (activities.length > 0) {
            router.push(activities[0].route as any);
          }
              }}
              gradientColors={[...colors.gradients.primary]}
              icon={<Play size={24} color={colors.white} />}
              size="large"
            />
            <GradientButton
              title="View All Modules"
              onPress={() => router.push('/(tabs)/modules')}
              gradientColors={[colors.backgroundSecondary, colors.backgroundTertiary]}
              size="large"
            />
          </FlexColumn>
        </SectionLayout>

        <Spacer height={40} />
      </ContentLayout>

      {/* Add Goal Modal */}
      <EnhancedModal
        visible={showAddGoalModal}
        onClose={closeAddGoalModal}
        title="Add Custom Goal"
        size="small"
      >
        <FlexColumn gap={16}>
          <EnhancedInput
              value={newGoalText}
              onChangeText={setNewGoalText}
            placeholder="Enter your custom goal..."
            multiline={true}
            numberOfLines={3}
          />
          <FlexRow justifyContent="space-around" gap={16}>
            <GradientButton
              title="Cancel"
              onPress={closeAddGoalModal}
              gradientColors={[colors.secondary, colors.secondaryLight]}
              size="small"
            />
            <GradientButton
              title="Add Goal"
                onPress={addCustomGoal}
              gradientColors={[colors.primary, colors.primaryLight]}
              size="small"
            />
          </FlexRow>
        </FlexColumn>
      </EnhancedModal>
    </ScreenLayout>
  );
}

const styles = StyleSheet.create({
  // Guest styles
  guestTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.white,
    textAlign: 'center',
  },
  guestSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 24,
  },
  guestButton: {
    marginTop: 16,
  },

  // Header styles
  greeting: {
    fontSize: 16,
    color: colors.textInverse,
    fontWeight: '500',
    opacity: 0.9,
  },
  coupleNames: {
    fontSize: 28,
    color: colors.textInverse,
    fontWeight: '700',
  },
  tagline: {
    fontSize: 14,
    color: colors.textInverse,
    fontWeight: '400',
    opacity: 0.8,
  },

  // Progress styles
  progressPercent: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
  },
  progressSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  progressText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },

  // Quick access styles
  quickAccessTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  quickAccessSubtitle: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    lineHeight: 20,
  },
  quickAccessButton: {
    backgroundColor: colors.whiteOverlayMedium,
    borderWidth: 1,
    borderColor: colors.whiteOverlayStrong,
  },

  // Goals styles
  addGoalButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  noGoalsText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 10,
  },

  // Quick link styles
  quickLinkTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
  },
  quickLinkSubtitle: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
    lineHeight: 20,
  },

  // Summary styles
  summaryTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.lightPink,
  },
  summaryText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});