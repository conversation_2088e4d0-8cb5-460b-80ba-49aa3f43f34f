import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Lock, CircleCheck as CheckCircle, Clock, Users, Heart, MessageCircle, Calendar, Star, ArrowRight } from 'lucide-react-native';
import { router } from 'expo-router';
import { usePointsSystemSupabase } from '../../hooks/usePointsSystemSupabase';
import { useAuth } from '../../contexts/AuthContext';
import { colors } from '../../utils/colors';
import HamburgerMenu from '../../components/HamburgerMenu';

export default function ModulesScreen() {
  const { isInitialized } = useAuth();
  const { totalPoints, addPoints } = usePointsSystemSupabase();

  // Don't render until auth is initialized
  if (!isInitialized) {
    return null;
  }


  // Fallback colors in case colors object is undefined
  const fallbackColors = {
    primary: '#5CC7C2',
    secondary: '#FF9B91',
    gradients: {
      primary: ['#5CC7C2', '#7DDAD4'],
      warm: ['#FF9B91', '#E8C7A0'],
      secondary: ['#FF9B91', '#FFB39E'],
      header: ['#5CC7C2', '#FF9B91']
    }
  };

  const safeColors = colors || fallbackColors;

  // Simple test modules array
  const modules = [
    {
      week: 1,
      title: 'Getting to Know You',
      description: 'Rediscover each other through fun activities',
      completed: false,
      locked: false,
      activities: ['The Match Game', 'Date Night Plan', 'Chat Prompts', 'Soft Start-Up'],
      color: safeColors.gradients.primary,
      points: 0,
    },
    {
      week: 0,
      title: 'Date Night Ideas',
      description: 'Plan perfect evenings with activities + meals',
      completed: false,
      locked: false,
      activities: ['Date Night Planning', 'Meal Decision-Making', 'Combined Suggestions', 'Evening Planning'],
      color: safeColors.gradients.warm,
      points: 0,
      isSpecial: true,
    }
  ];

  // Ensure modules array is always defined
  const safeModules = modules || [];


  // Check if activity is completed
  const isActivityCompleted = (moduleId: number, activityName: string) => {
    // TODO: Implement activity completion tracking with Supabase
    return false;
  };

  const renderActivityIcon = (activity: string) => {
    switch (activity) {
      case 'Match Game':
      case 'Active Listening':
      case 'Create a Playlist':
        return <Users size={16} color={colors.white} />;
      case 'Date Night Plan':
      case 'Alphabet Date Night':
        return <Calendar size={16} color={colors.white} />;
      case 'Chat Prompts':
      case 'Soft Start-Up':
      case '5:1 Ratio':
      case 'Chat Prompts':
      case 'Emotional Regulation':
        return <MessageCircle size={16} color={colors.white} />;
      case 'Movie Theme Night':
        return <Calendar size={16} color={colors.white} />;
      default:
        return <Heart size={16} color={colors.white} />;
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />
      
      {/* Header */}
      <LinearGradient
        colors={safeColors.gradients.header || [safeColors.primary, safeColors.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Weekly Modules</Text>
        <Text style={styles.headerSubtitle}>Complete activities together to strengthen your relationship</Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {Array.isArray(safeModules) && safeModules.length > 0 ? (
          safeModules.map((module, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.moduleCard, module.locked && styles.moduleCardLocked]}
              disabled={module.locked}
              onPress={() => {
                if (module.week === 0 && module.isSpecial) {
                  router.push('/(tabs)/date-night');
                } else if (module.week === 1) {
                  router.push('/week-one');
                }
              }}
            >
              <View style={styles.moduleHeader}>
                <View style={styles.moduleWeek}>
                  <Text style={styles.moduleWeekText}>
                    {module.isSpecial ? '✨ Special' : `Week ${module.week}`}
                  </Text>
                </View>
                
                <View style={styles.moduleStatus}>
                  {module.completed ? (
                    <CheckCircle size={24} color={colors.success} />
                  ) : module.locked ? (
                    <Lock size={24} color={colors.textTertiary} />
                  ) : (
                    <Clock size={24} color={colors.warning} />
                  )}
                </View>
              </View>

              <Text style={[styles.moduleTitle, module.locked && styles.moduleTitleLocked]}>
                {module.title}
              </Text>
              <Text style={[styles.moduleDescription, module.locked && styles.moduleDescriptionLocked]}>
                {module.description}
              </Text>

              {/* Points Display */}
              <View style={styles.pointsContainer}>
                <Star size={16} color={colors.warning} />
                <Text style={styles.pointsText}>{module.points} points earned</Text>
              </View>

              {/* Activities */}
              <View style={styles.activitiesContainer}>
                {Array.isArray(module.activities) && module.activities.length > 0 ? (
                  module.activities.map((activity, activityIndex) => (
                    <TouchableOpacity
                      key={activityIndex}
                      style={[
                        styles.activityChip,
                        isActivityCompleted(module.week, activity) && styles.activityChipCompleted
                      ]}
                      disabled={module.locked}
                      onPress={() => {
                        // Activity pressed - could add functionality here
                      }}
                    >
                      <LinearGradient
                        colors={module.locked ? [colors.backgroundTertiary, colors.borderLight] : module.color}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                        style={styles.activityChipGradient}
                      >
                        {renderActivityIcon(activity)}
                        <Text style={[
                          styles.activityChipText,
                          module.locked && styles.activityChipTextLocked
                        ]}>
                          {activity}
                        </Text>
                        {isActivityCompleted(module.week, activity) && (
                          <CheckCircle size={14} color={colors.white} style={{ marginLeft: 4 }} />
                        )}
                      </LinearGradient>
                    </TouchableOpacity>
                  ))
                ) : null}
              </View>

              {!module.locked && (
                <TouchableOpacity 
                  style={styles.startButton}
                  onPress={() => {
                    if (module.week === 1) {
                      router.push('/week-one');
                    }
                  }}
                >
                  <LinearGradient
                    colors={module.completed ? [colors.success, colors.success] : module.color}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.startButtonGradient}
                  >
                    <Text style={styles.startButtonText}>
                      {module.completed ? 'Review' : 'Start Module'}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              )}

              {module.locked && (
                <View style={styles.lockedMessage}>
                  <Text style={styles.lockedText}>
                    Complete previous modules to unlock
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))
        ) : null}

        <View style={styles.completionCard}>
          <LinearGradient
            colors={[colors.backgroundOrange, colors.backgroundOrange]}
            style={styles.completionGradient}
          >
            <Heart size={32} color={colors.primary} />
            <Text style={styles.completionTitle}>Journey Completion</Text>
            <Text style={styles.completionText}>
              After completing all 12 weeks, unlock your personalized Relationship Yearbook - 
              a beautiful keepsake of your growth together.
            </Text>
            <View style={styles.totalPointsDisplay}>
              <Star size={20} color={colors.warning} />
              <Text style={styles.totalPointsText}>
                Total Points: {totalPoints}
              </Text>
            </View>
          </LinearGradient>
        </View>
      </ScrollView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.textInverse,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.textInverse,
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  moduleCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  moduleCardLocked: {
    opacity: 0.6,
  },
  moduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  moduleWeek: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  moduleWeekText: {
    color: colors.textInverse,
    fontSize: 12,
    fontWeight: '600',
  },
  moduleStatus: {
    // Icon container
  },
  moduleTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  moduleTitleLocked: {
    color: colors.textTertiary,
  },
  moduleDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  moduleDescriptionLocked: {
    color: colors.borderMedium,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  pointsText: {
    color: colors.goldenAmber,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  activitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  activityChip: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  activityChipCompleted: {
    opacity: 0.6,
  },
  activityChipGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  activityChipText: {
    color: colors.textInverse,
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  activityChipTextLocked: {
    color: colors.textTertiary,
  },
  startButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  startButtonGradient: {
    padding: 14,
    alignItems: 'center',
  },
  startButtonText: {
    color: colors.textInverse,
    fontSize: 16,
    fontWeight: '600',
  },
  lockedMessage: {
    backgroundColor: colors.backgroundGray,
    padding: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  lockedText: {
    color: colors.textTertiary,
    fontSize: 14,
    fontWeight: '500',
  },
  completionCard: {
    marginBottom: 40,
    borderRadius: 16,
    overflow: 'hidden',
  },
  completionGradient: {
    padding: 24,
    alignItems: 'center',
  },
  completionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginTop: 12,
    marginBottom: 8,
  },
  completionText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  totalPointsDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  totalPointsText: {
    color: colors.goldenAmber,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
});