#!/usr/bin/env node

// Test script to verify API key validation
console.log('Testing API key validation...');

// Test 1: Missing environment variables
console.log('\n1. Testing missing environment variables...');
delete process.env.EXPO_PUBLIC_SUPABASE_URL;
delete process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

try {
  require('./lib/supabase/client.ts');
  console.log('❌ FAILED: Should have thrown error for missing env vars');
} catch (error) {
  console.log('✅ PASSED: Correctly threw error for missing env vars');
  console.log('   Error:', error.message);
}

// Test 2: Placeholder values
console.log('\n2. Testing placeholder values...');
process.env.EXPO_PUBLIC_SUPABASE_URL = 'YOUR_SUPABASE_URL';
process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY = 'YOUR_SUPABASE_ANON_KEY';

try {
  // Clear require cache
  delete require.cache[require.resolve('./lib/supabase/client.ts')];
  require('./lib/supabase/client.ts');
  console.log('❌ FAILED: Should have thrown error for placeholder values');
} catch (error) {
  console.log('✅ PASSED: Correctly threw error for placeholder values');
  console.log('   Error:', error.message);
}

// Test 3: Invalid URL format
console.log('\n3. Testing invalid URL format...');
process.env.EXPO_PUBLIC_SUPABASE_URL = 'not-a-valid-url';
process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY = 'valid_key';

try {
  // Clear require cache
  delete require.cache[require.resolve('./lib/supabase/client.ts')];
  require('./lib/supabase/client.ts');
  console.log('❌ FAILED: Should have thrown error for invalid URL');
} catch (error) {
  console.log('✅ PASSED: Correctly threw error for invalid URL');
  console.log('   Error:', error.message);
}

// Test 4: Valid configuration
console.log('\n4. Testing valid configuration...');
process.env.EXPO_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY = 'valid_test_key';

try {
  // Clear require cache
  delete require.cache[require.resolve('./lib/supabase/client.ts')];
  require('./lib/supabase/client.ts');
  console.log('✅ PASSED: Valid configuration accepted');
} catch (error) {
  console.log('❌ FAILED: Valid configuration should not throw error');
  console.log('   Error:', error.message);
}

console.log('\nAPI key validation tests completed!');
