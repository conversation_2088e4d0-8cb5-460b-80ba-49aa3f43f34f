# Everlasting Us - Relationship Growth App

A beautiful, interactive app designed to help couples strengthen their relationships through weekly modules, activities, and meaningful conversations.

## Week Two: Strengths Bingo

### Overview
Week Two introduces the Strengths Bingo module, designed to help couples celebrate each other's strengths, create a playful date night, and learn about the research-backed 5:1 positive-to-negative interaction ratio.

### Features

#### 1. Strengths Bingo Grid
- **Interactive 5x5 grid** with 25 different strengths
- **Tap any square** to add examples of when your partner demonstrated that strength
- **Save examples** with timestamps for your scrapbook
- **Progress tracking** showing how many strengths you've celebrated together
- **Visual feedback** with checkmarks and color changes

**Strengths Include:**
- Creative Genius, Great Listener, Empathy
- Champion Problem Solver, Always Positive, Supportive
- Hard Worker, Adventurous, Loyal, Kind
- Patient, Optimistic, Choose Your Own, Helpful
- Romantic, Reliable, Thoughtful, Caring
- Quick Thinker, Honest, Independent
- Strong Communicator, Level-Headed, Motivated, Funny

#### 2. Date Night Plan: Cooking Showdown
- **Activity**: "Plate It Like a Pro" cooking challenge
- **Description**: Team up to make a full meal, each person picks one part (main, side, or garnish)
- **Goal**: Focus on presentation — winner is the most beautiful and creative plate
- **Inputs**: When and Where fields (optional, saved to profile)
- **Status**: Option to mark as Completed

#### 3. Date Night Chat Prompts
Two engaging conversation starters:
1. **Drag King/Queen**: "If you were a drag king/queen, what would be your name and best performance quality?"
2. **Meaningful Gift**: "What was the most meaningful gift you've ever received? Why was it so special?"

#### 4. Relationship Skills Toolkit: The 5:1 Ratio
- **Concept**: Strong relationships have at least 5 positive interactions for every 1 negative
- **Practice**: 5 text boxes for couples to write feel-good moments from the past few days
- **Tip**: Encourages regular repetition of this exercise
- **Data**: All entries saved to Scrapbook for future reference

### Technical Implementation

#### Data Structure
- **StrengthsBingoSquare**: Individual bingo squares with selection state and examples
- **DateNightPlan**: Date night details with completion status
- **ChatPrompt**: Conversation prompts with partner responses
- **FiveToOneRatio**: Positive moments tracking
- **Progress Tracking**: 4/4 completion indicator

#### Navigation
- **Step-by-step progression** through all four sections
- **Revisitable**: Can return to any completed section
- **Progress persistence**: All data saved locally and in Scrapbook
- **Navigation**: Save & Continue / Come Back Later options

#### UI/UX Features
- **Playful design** with colorful squares and checkmarks
- **Warm, affirming language** throughout the experience
- **Responsive grid** that works on all screen sizes
- **Modal interactions** for adding strength examples
- **Progress indicators** showing completion status

### How to Use

1. **Navigate to Modules** from the main tab navigation
2. **Select Week Two: Strengths Bingo**
3. **Complete each section** in order or jump between sections
4. **Save your progress** as you go
5. **Return later** to continue or review your responses

### Data Persistence
- All responses automatically saved to local storage
- Completed data feeds into the Scrapbook feature
- Progress tracking across sessions
- Examples and responses preserved for future reference

### Future Enhancements
- Bingo win detection (rows, columns, diagonals)
- Achievement badges for completing the grid
- Social sharing of completed strengths
- Weekly reminders to practice the 5:1 ratio
- Integration with other relationship tracking features

## 🚀 Quick Start

### Development
```bash
# Start development server
npm run dev

# Start with specific platform
npx expo start --web
npx expo start --ios
npx expo start --android
```

### Building
```bash
# Build for web
npm run build:web

# Build for mobile
npx expo run:ios
npx expo run:android
```

📖 **For detailed build instructions, see [BUILD_GUIDE.md](./BUILD_GUIDE.md)**

---

*Built with React Native, Expo, and lots of love for helping couples grow together.*
