export interface MatchGameQuestion {
  id: string;
  question: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  type: 'guess' | 'text' | 'list';
}

export const MATCH_GAME_QUESTIONS: MatchGameQuestion[] = [
  // FOOD & DINING (50 questions)
  { id: 'comfort_meal', question: 'My comfort meal', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'favorite_cuisine', question: 'My favorite type of cuisine', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'breakfast_preference', question: 'My ideal breakfast', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'snack_craving', question: 'My go-to late night snack', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'dessert_weakness', question: 'My dessert weakness', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'cooking_style', question: 'My cooking style in the kitchen', category: 'Food', difficulty: 'medium', type: 'guess' },
  { id: 'restaurant_choice', question: 'My restaurant choice for special occasions', category: 'Food', difficulty: 'medium', type: 'guess' },
  { id: 'food_adventure', question: 'The most adventurous food I\'ve tried', category: 'Food', difficulty: 'medium', type: 'guess' },
  { id: 'kitchen_role', question: 'My role when we cook together', category: 'Food', difficulty: 'medium', type: 'guess' },
  { id: 'food_memory', question: 'A food that brings back childhood memories', category: 'Food', difficulty: 'hard', type: 'guess' },
  { id: 'dietary_preference', question: 'My dietary preference or restriction', category: 'Food', difficulty: 'medium', type: 'guess' },
  { id: 'coffee_order', question: 'My coffee order', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'pizza_toppings', question: 'My favorite pizza toppings', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'ice_cream_flavor', question: 'My ice cream flavor', category: 'Food', difficulty: 'easy', type: 'guess' },
  { id: 'cooking_fear', question: 'A dish I\'m afraid to cook', category: 'Food', difficulty: 'medium', type: 'guess' },
  { id: 'food_ritual', question: 'My food ritual or habit', category: 'Food', difficulty: 'hard', type: 'guess' },

  // ENTERTAINMENT & MEDIA (50 questions)
  { id: 'movie_rewatch', question: 'A movie I could rewatch over and over', category: 'Entertainment', difficulty: 'easy', type: 'guess' },
  { id: 'tv_show_binge', question: 'My favorite TV show to binge', category: 'Entertainment', difficulty: 'easy', type: 'guess' },
  { id: 'book_genre', question: 'My favorite book genre', category: 'Entertainment', difficulty: 'easy', type: 'guess' },
  { id: 'podcast_topic', question: 'My favorite podcast topic', category: 'Entertainment', difficulty: 'medium', type: 'guess' },
  { id: 'game_preference', question: 'My preferred type of game', category: 'Entertainment', difficulty: 'medium', type: 'guess' },
  { id: 'celebrity_crush', question: 'My celebrity crush', category: 'Entertainment', difficulty: 'easy', type: 'guess' },
  { id: 'movie_genre', question: 'My favorite movie genre', category: 'Entertainment', difficulty: 'easy', type: 'guess' },
  { id: 'concert_experience', question: 'My best concert experience', category: 'Entertainment', difficulty: 'medium', type: 'guess' },
  { id: 'streaming_service', question: 'My most used streaming service', category: 'Entertainment', difficulty: 'easy', type: 'guess' },
  { id: 'theater_preference', question: 'My theater preference', category: 'Entertainment', difficulty: 'medium', type: 'guess' },

  // PERSONAL & LIFESTYLE (50 questions)
  { id: 'guilty_pleasure', question: 'My guilty pleasure', category: 'Personal', difficulty: 'medium', type: 'guess' },
  { id: 'stress_relief', question: 'My go-to stress relief method', category: 'Personal', difficulty: 'medium', type: 'guess' },
  { id: 'morning_routine', question: 'My morning routine', category: 'Personal', difficulty: 'medium', type: 'guess' },
  { id: 'bedtime_habit', question: 'My bedtime habit', category: 'Personal', difficulty: 'medium', type: 'guess' },
  { id: 'personal_quirk', question: 'My most endearing quirk', category: 'Personal', difficulty: 'hard', type: 'guess' },
  { id: 'fear_phobia', question: 'My biggest fear or phobia', category: 'Personal', difficulty: 'hard', type: 'guess' },
  { id: 'pet_peeve', question: 'My biggest pet peeve', category: 'Personal', difficulty: 'medium', type: 'guess' },
  { id: 'confidence_boost', question: 'What makes me feel most confident', category: 'Personal', difficulty: 'medium', type: 'guess' },
  { id: 'vulnerability', question: 'When I feel most vulnerable', category: 'Personal', difficulty: 'hard', type: 'guess' },
  { id: 'personal_growth', question: 'An area I want to grow in', category: 'Personal', difficulty: 'medium', type: 'guess' },

  // MUSIC & DANCE (40 questions)
  { id: 'dance_song', question: 'If this song is on, I\'m on the dance floor', category: 'Music', difficulty: 'easy', type: 'guess' },
  { id: 'karaoke_song', question: 'My karaoke song', category: 'Music', difficulty: 'easy', type: 'guess' },
  { id: 'road_trip_playlist', question: 'My road trip playlist theme', category: 'Music', difficulty: 'medium', type: 'guess' },
  { id: 'workout_music', question: 'My workout music preference', category: 'Music', difficulty: 'medium', type: 'guess' },
  { id: 'concert_dream', question: 'My dream concert to attend', category: 'Music', difficulty: 'medium', type: 'guess' },
  { id: 'musical_instrument', question: 'An instrument I wish I could play', category: 'Music', difficulty: 'medium', type: 'guess' },
  { id: 'song_memory', question: 'A song that brings back memories', category: 'Music', difficulty: 'hard', type: 'guess' },
  { id: 'music_genre', question: 'My favorite music genre', category: 'Music', difficulty: 'easy', type: 'guess' },
  { id: 'dance_style', question: 'My dance style', category: 'Music', difficulty: 'medium', type: 'guess' },
  { id: 'playlist_curation', question: 'How I organize my playlists', category: 'Music', difficulty: 'hard', type: 'guess' },

  // TRAVEL & ADVENTURE (40 questions)
  { id: 'dream_vacation', question: 'My dream vacation', category: 'Travel', difficulty: 'easy', type: 'guess' },
  { id: 'travel_style', question: 'How I like to travel', category: 'Travel', difficulty: 'medium', type: 'guess' },
  { id: 'bucket_list_destination', question: 'My bucket list destination', category: 'Travel', difficulty: 'medium', type: 'guess' },
  { id: 'travel_fear', question: 'My biggest travel fear', category: 'Travel', difficulty: 'medium', type: 'guess' },
  { id: 'souvenir_preference', question: 'My souvenir preference', category: 'Travel', difficulty: 'hard', type: 'guess' },
  { id: 'adventure_activity', question: 'My preferred adventure activity', category: 'Travel', difficulty: 'medium', type: 'guess' },
  { id: 'travel_companion', question: 'My ideal travel companion', category: 'Travel', difficulty: 'medium', type: 'guess' },
  { id: 'local_experience', question: 'My preferred local experience', category: 'Travel', difficulty: 'hard', type: 'guess' },
  { id: 'travel_memory', question: 'My best travel memory', category: 'Travel', difficulty: 'medium', type: 'guess' },
  { id: 'accommodation_style', question: 'My accommodation preference', category: 'Travel', difficulty: 'medium', type: 'guess' },

  // RELATIONSHIP & LOVE (50 questions)
  { id: 'love_language', question: 'My primary love language', category: 'Relationship', difficulty: 'medium', type: 'guess' },
  { id: 'romantic_gesture', question: 'My favorite romantic gesture', category: 'Relationship', difficulty: 'medium', type: 'guess' },
  { id: 'date_idea', question: 'My ideal date idea', category: 'Relationship', difficulty: 'easy', type: 'guess' },
  { id: 'relationship_goal', question: 'My biggest relationship goal', category: 'Relationship', difficulty: 'medium', type: 'guess' },
  { id: 'communication_style', question: 'My communication style in arguments', category: 'Relationship', difficulty: 'hard', type: 'guess' },
  { id: 'quality_time', question: 'My preferred way to spend quality time', category: 'Relationship', difficulty: 'medium', type: 'guess' },
  { id: 'apology_style', question: 'How I prefer to apologize', category: 'Relationship', difficulty: 'hard', type: 'guess' },
  { id: 'support_style', question: 'How I show support when you\'re stressed', category: 'Relationship', difficulty: 'medium', type: 'guess' },
  { id: 'future_vision', question: 'My vision for our future', category: 'Relationship', difficulty: 'hard', type: 'guess' },
  { id: 'relationship_fear', question: 'My biggest relationship fear', category: 'Relationship', difficulty: 'hard', type: 'guess' },

  // WORK & AMBITION (30 questions)
  { id: 'career_dream', question: 'My career dream', category: 'Work', difficulty: 'medium', type: 'guess' },
  { id: 'work_style', question: 'My work style', category: 'Work', difficulty: 'medium', type: 'guess' },
  { id: 'stress_response', question: 'How I handle work stress', category: 'Work', difficulty: 'medium', type: 'guess' },
  { id: 'achievement_pride', question: 'My proudest achievement', category: 'Work', difficulty: 'medium', type: 'guess' },
  { id: 'skill_development', question: 'A skill I want to develop', category: 'Work', difficulty: 'medium', type: 'guess' },
  { id: 'work_environment', question: 'My ideal work environment', category: 'Work', difficulty: 'medium', type: 'guess' },
  { id: 'leadership_style', question: 'My leadership style', category: 'Work', difficulty: 'hard', type: 'guess' },
  { id: 'work_life_balance', question: 'My approach to work-life balance', category: 'Work', difficulty: 'hard', type: 'guess' },
  { id: 'mentor_quality', question: 'A quality I look for in a mentor', category: 'Work', difficulty: 'hard', type: 'guess' },
  { id: 'failure_lesson', question: 'A failure that taught me the most', category: 'Work', difficulty: 'hard', type: 'guess' },

  // HOBBIES & INTERESTS (40 questions)
  { id: 'creative_outlet', question: 'My creative outlet', category: 'Hobbies', difficulty: 'medium', type: 'guess' },
  { id: 'weekend_activity', question: 'My favorite weekend activity', category: 'Hobbies', difficulty: 'easy', type: 'guess' },
  { id: 'collection_hobby', question: 'Something I collect', category: 'Hobbies', difficulty: 'medium', type: 'guess' },
  { id: 'learning_interest', question: 'Something I want to learn', category: 'Hobbies', difficulty: 'medium', type: 'guess' },
  { id: 'outdoor_activity', question: 'My favorite outdoor activity', category: 'Hobbies', difficulty: 'easy', type: 'guess' },
  { id: 'indoor_hobby', question: 'My favorite indoor hobby', category: 'Hobbies', difficulty: 'easy', type: 'guess' },
  { id: 'sport_interest', question: 'My sport interest', category: 'Hobbies', difficulty: 'medium', type: 'guess' },
  { id: 'artistic_skill', question: 'An artistic skill I have', category: 'Hobbies', difficulty: 'medium', type: 'guess' },
  { id: 'gaming_preference', question: 'My gaming preference', category: 'Hobbies', difficulty: 'medium', type: 'guess' },
  { id: 'craft_hobby', question: 'A craft I enjoy', category: 'Hobbies', difficulty: 'medium', type: 'guess' },

  // FAMILY & FRIENDS (30 questions)
  { id: 'family_tradition', question: 'My favorite family tradition', category: 'Family', difficulty: 'medium', type: 'guess' },
  { id: 'friend_quality', question: 'A quality I value most in friends', category: 'Family', difficulty: 'medium', type: 'guess' },
  { id: 'parenting_style', question: 'My parenting style', category: 'Family', difficulty: 'hard', type: 'guess' },
  { id: 'family_role', question: 'My role in my family', category: 'Family', difficulty: 'medium', type: 'guess' },
  { id: 'friendship_approach', question: 'How I approach making friends', category: 'Family', difficulty: 'hard', type: 'guess' },
  { id: 'family_memory', question: 'My best family memory', category: 'Family', difficulty: 'medium', type: 'guess' },
  { id: 'conflict_resolution', question: 'How I resolve conflicts with family', category: 'Family', difficulty: 'hard', type: 'guess' },
  { id: 'support_network', question: 'My support network', category: 'Family', difficulty: 'medium', type: 'guess' },
  { id: 'family_goal', question: 'My family goal', category: 'Family', difficulty: 'medium', type: 'guess' },
  { id: 'friendship_maintenance', question: 'How I maintain friendships', category: 'Family', difficulty: 'hard', type: 'guess' },

  // VALUES & BELIEFS (20 questions)
  { id: 'core_value', question: 'My most important core value', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'life_principle', question: 'A life principle I live by', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'moral_compass', question: 'What guides my moral decisions', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'spiritual_belief', question: 'My spiritual belief', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'ethical_standard', question: 'My ethical standard', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'life_mission', question: 'My life mission', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'personal_philosophy', question: 'My personal philosophy', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'value_conflict', question: 'A value I struggle with', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'legacy_goal', question: 'The legacy I want to leave', category: 'Values', difficulty: 'hard', type: 'guess' },
  { id: 'meaning_purpose', question: 'What gives my life meaning', category: 'Values', difficulty: 'hard', type: 'guess' }
];

export const getRandomQuestions = (count: number = 10): MatchGameQuestion[] => {
  const shuffled = [...MATCH_GAME_QUESTIONS].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getQuestionsByCategory = (category: string, count: number = 10): MatchGameQuestion[] => {
  const categoryQuestions = MATCH_GAME_QUESTIONS.filter(q => q.category === category);
  const shuffled = [...categoryQuestions].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getQuestionsByDifficulty = (difficulty: 'easy' | 'medium' | 'hard', count: number = 10): MatchGameQuestion[] => {
  const difficultyQuestions = MATCH_GAME_QUESTIONS.filter(q => q.difficulty === difficulty);
  const shuffled = [...difficultyQuestions].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getBalancedQuestions = (count: number = 10): MatchGameQuestion[] => {
  const easyCount = Math.ceil(count * 0.4); // 40% easy
  const mediumCount = Math.ceil(count * 0.4); // 40% medium  
  const hardCount = count - easyCount - mediumCount; // 20% hard

  const easyQuestions = getQuestionsByDifficulty('easy', easyCount);
  const mediumQuestions = getQuestionsByDifficulty('medium', mediumCount);
  const hardQuestions = getQuestionsByDifficulty('hard', hardCount);

  const allQuestions = [...easyQuestions, ...mediumQuestions, ...hardQuestions];
  return allQuestions.sort(() => 0.5 - Math.random()); // Shuffle final result
};
