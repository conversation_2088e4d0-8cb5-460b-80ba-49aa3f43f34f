/**
 * Color palette for the couples app
 * Modernized for 2025 with warm, balanced, and gender-neutral tones
 */

export const colors = {
  // Primary colors (refreshed)
  softCoral: '#FF9B91',      // softer coral, inviting without being too pink
  lightTeal: '#5CC7C2',      // lighter teal, fresh & modern
  lavenderGray: '#C4C3D0',   // soft, neutral balance
  warmSand: '#E8C7A0',       // warmer sand, cozy & grounding
  goldenAmber: '#FFC857',    // softer amber, playful energy
  mintGreen: '#98E0AD',      // lighter mint, positive accents
  offWhite: '#FAFAFA',       // clean background
  charcoal: '#1E1E1E',       // deeper charcoal for modern dark mode

  // Semantic colors
  white: '#FFFFFF',
  black: '#000000',
  
  // Text colors
  textPrimary: '#1E1E1E',    // updated charcoal for text
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: '#FFFFFF',
  
  // Background colors
  background: '#FAFAFA',           // alias for backgroundPrimary
  backgroundPrimary: '#FAFAFA',
  backgroundSecondary: '#FFFFFF',
  backgroundTertiary: '#F3F4F6',
  
  // Border colors
  borderLight: '#E5E7EB',
  borderMedium: '#D1D5DB',
  borderDark: '#9CA3AF',
  
  // Status colors
  success: '#98E0AD',        // mint green
  warning: '#FFC857',        // golden amber
  error: '#FF9B91',          // coral
  info: '#5CC7C2',           // teal
  
  // Interactive colors
  primary: '#5CC7C2',        // light teal
  primaryLight: '#7DDAD4',   // aqua hover
  primaryDark: '#2E8C87',    // teal pressed
  secondary: '#FF9B91',      // peach coral
  secondaryLight: '#FFB39E', // coral hover
  secondaryDark: '#E5786A',  // coral pressed
  
  // Accent colors
  accent1: '#E8C7A0',        // warm sand
  accent2: '#C4C3D0',        // lavender gray
  accent3: '#FFC857',        // amber
  accent4: '#98E0AD',        // mint green
  
  // Additional accent colors
  deepCoral: '#E5786A',      // deeper coral variant
  tealBlue: '#2E8C87',       // deeper teal variant
  
  // Additional utility colors (kept for consistency)
  blue: '#60A5FA',
  blueDark: '#3B82F6',
  green: '#10B981',
  greenDark: '#059669',
  orange: '#F59E0B',
  orangeDark: '#D97706',
  red: '#EF4444',
  redDark: '#DC2626',
  purple: '#8B5CF6',
  purpleDark: '#7C3AED',
  cyan: '#06B6D4',
  cyanDark: '#0891B2',
  lime: '#84CC16',
  limeDark: '#65A30D',
  orangeRed: '#F97316',
  orangeRedDark: '#EA580C',
  pink: '#EC4899',
  pinkDark: '#DB2777',
  indigo: '#6366F6',
  indigoDark: '#4F46E5',
  lightPink: '#F8BBD9',
  lightPurple: '#E879F9',
  lightPurpleDark: '#C084FC',
  
  // Text utilities
  textGray: '#374151',
  textGrayLight: '#1F2937',
  
  // Background utilities
  backgroundGray: '#F9FAFB',
  backgroundPink: '#FEF7FF',
  backgroundOrange: '#FEF3E2',

  // Gradient colors
  gradients: {
    primary: ['#5CC7C2', '#7DDAD4', '#2E8C87'],
    secondary: ['#FF9B91', '#FFB39E', '#E5786A'],
    warm: ['#FF9B91', '#E8C7A0', '#FFC857'],
    cool: ['#5CC7C2', '#C4C3D0', '#98E0AD'],
    sunset: ['#FF9B91', '#FFC857', '#E8C7A0'],
    ocean: ['#5CC7C2', '#7DDAD4', '#C4C3D0'],
    header: ['#5CC7C2', '#FF9B91', '#FFC857'],
    card: ['#FFFFFF', '#FAFAFA'],
    
    // Module-specific gradients (unchanged for consistency)
    moduleBlue: ['#60A5FA', '#3B82F6'],
    moduleGreen: ['#10B981', '#059669'],
    moduleOrange: ['#F59E0B', '#D97706'],
    moduleRed: ['#EF4444', '#DC2626'],
    modulePurple: ['#8B5CF6', '#7C3AED'],
    moduleCyan: ['#06B6D4', '#0891B2'],
    moduleLime: ['#84CC16', '#65A30D'],
    moduleOrangeRed: ['#F97316', '#EA580C'],
    modulePink: ['#EC4899', '#DB2777'],
    moduleIndigo: ['#6366F6', '#4F46E5'],
    moduleLightPurple: ['#E879F9', '#C084FC'],
    moduleLightPink: ['#F8BBD9', '#E879F9'],
    
    // Badge gradients
    badgeGreen: ['#10B981', '#059669'],
    badgeBlue: ['#60A5FA', '#3B82F6'],
    badgeOrange: ['#F59E0B', '#D97706'],
    badgePurple: ['#E879F9', '#C084FC'],
    badgeRed: ['#EF4444', '#DC2626'],
    badgePink: ['#F8BBD9', '#E879F9'],
    
    // Status gradients
    success: ['#10B981', '#059669'],
    warning: ['#F59E0B', '#D97706'],
    error: ['#EF4444', '#DC2626'],
    info: ['#5CC7C2', '#7DDAD4'],
  },
  
  // Shadow colors
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.1)',
  shadowMedium: 'rgba(0, 0, 0, 0.2)',
  shadowDark: 'rgba(0, 0, 0, 0.3)',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',
  
  // White overlay colors
  whiteOverlayLight: 'rgba(255, 255, 255, 0.1)',
  whiteOverlayMedium: 'rgba(255, 255, 255, 0.2)',
  whiteOverlayStrong: 'rgba(255, 255, 255, 0.3)',
  whiteOverlayStronger: 'rgba(255, 255, 255, 0.9)',
} as const;

export type ColorKey = keyof typeof colors;

export const getColorWithOpacity = (color: string, opacity: number): string => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const getGradientColors = (gradientName: keyof typeof colors.gradients): readonly string[] => {
  return colors.gradients[gradientName];
};

export default colors;