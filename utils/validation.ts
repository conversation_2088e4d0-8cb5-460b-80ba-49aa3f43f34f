/**
 * Input validation utilities for security
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitizedValue?: string;
}

/**
 * Sanitize text input to prevent XSS and injection attacks
 */
export function sanitizeText(input: string, maxLength: number = 1000): ValidationResult {
  if (!input || typeof input !== 'string') {
    return { isValid: false, error: 'Input must be a non-empty string' };
  }

  if (input.length > maxLength) {
    return { isValid: false, error: `Input must be ${maxLength} characters or less` };
  }

  // Remove potentially dangerous characters and patterns
  let sanitized = input
    .trim()
    .replace(/[<>]/g, '') // Remove < and > to prevent HTML injection
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, ''); // Remove iframe tags

  // Check for remaining suspicious patterns
  if (/<[^>]*>/.test(sanitized)) {
    return { isValid: false, error: 'Input contains invalid HTML-like content' };
  }

  if (sanitized.length === 0) {
    return { isValid: false, error: 'Input cannot be empty after sanitization' };
  }

  return {
    isValid: true,
    sanitizedValue: sanitized
  };
}

/**
 * Validate and sanitize names
 */
export function validateName(name: string): ValidationResult {
  if (!name || typeof name !== 'string') {
    return { isValid: false, error: 'Name must be a non-empty string' };
  }

  if (name.length > 50) {
    return { isValid: false, error: 'Name must be 50 characters or less' };
  }

  if (name.length < 1) {
    return { isValid: false, error: 'Name must be at least 1 character' };
  }

  // Allow letters (including accented), spaces, hyphens, apostrophes, and dots
  // This covers most international names and common naming conventions
  if (!/^[\p{L}\s\-'.]+$/u.test(name)) {
    return { isValid: false, error: 'Name can only contain letters, spaces, hyphens, apostrophes, and dots' };
  }

  return {
    isValid: true,
    sanitizedValue: name.trim()
  };
}

/**
 * Validate and sanitize email addresses
 */
export function validateEmail(email: string): ValidationResult {
  if (!email || typeof email !== 'string') {
    return { isValid: false, error: 'Email must be a non-empty string' };
  }

  if (email.length > 254) {
    return { isValid: false, error: 'Email is too long' };
  }

  // Basic email validation regex
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Invalid email format' };
  }

  return {
    isValid: true,
    sanitizedValue: email.toLowerCase().trim()
  };
}

/**
 * Validate and sanitize URLs
 */
export function validateURL(url: string): ValidationResult {
  if (!url || typeof url !== 'string') {
    return { isValid: false, error: 'URL must be a non-empty string' };
  }

  if (url.length > 2048) {
    return { isValid: false, error: 'URL is too long' };
  }

  try {
    const urlObj = new URL(url);
    
    // Only allow HTTPS and HTTP protocols
    if (!['https:', 'http:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'Only HTTPS and HTTP protocols are allowed' };
    }

    // Check for suspicious domains or patterns
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|com|pif|scr|vbs|js)$/i, // Executable files
      /javascript:/i, // JavaScript protocol
      /data:/i, // Data protocol
      /vbscript:/i, // VBScript protocol
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(url)) {
        return { isValid: false, error: 'URL contains suspicious content' };
      }
    }

    return {
      isValid: true,
      sanitizedValue: url.trim()
    };
  } catch {
    return { isValid: false, error: 'Invalid URL format' };
  }
}

/**
 * Rate limiting utility
 */
export class RateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(identifier);

    if (!attempt || now > attempt.resetTime) {
      // Reset or create new attempt record
      this.attempts.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs
      });
      return true;
    }

    if (attempt.count >= this.maxAttempts) {
      return false;
    }

    attempt.count++;
    return true;
  }

  reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

/**
 * Global rate limiter instance
 */
export const globalRateLimiter = new RateLimiter(10, 60000); // 10 attempts per minute
