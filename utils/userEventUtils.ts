import { supabase } from '../lib/supabase/client';
import { logger } from './logger';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export interface UserEvent {
  id: string;
  user_id: string;
  event_name: string;
  created_at: string;
}

// Safe storage helper
async function safeStorageGetItem(key: string): Promise<string | null> {
  try {
    if (Platform.OS === 'web') {
      if (typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage.getItem(key);
      }
      return null;
    } else {
      return await AsyncStorage.getItem(key);
    }
  } catch (error) {
    return null;
  }
}

async function safeStorageSetItem(key: string, value: string): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem(key, value);
      }
    } else {
      await AsyncStorage.setItem(key, value);
    }
  } catch (error) {
    // Silently handle storage errors
  }
}

async function safeStorageRemoveItem(key: string): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.removeItem(key);
      }
    } else {
      await AsyncStorage.removeItem(key);
    }
  } catch (error) {
    // Silently handle storage errors
  }
}

// Helper functions for guest mode
export async function getGuestEvents(): Promise<UserEvent[]> {
  try {
    const eventsJson = await safeStorageGetItem('guest_events');
    return eventsJson ? JSON.parse(eventsJson) : [];
  } catch (error) {
    logger.error('Error getting guest events:', error);
    return [];
  }
}

export async function hasGuestEvent(eventName: string): Promise<boolean> {
  try {
    const events = await getGuestEvents();
    return events.some(event => event.event_name === eventName);
  } catch (error) {
    logger.error('Error checking guest event:', error);
    return false;
  }
}

export async function migrateGuestEventsToUser(userId: string): Promise<boolean> {
  try {
    const guestEvents = await getGuestEvents();
    
    if (guestEvents.length === 0) {
      return true; // No events to migrate
    }

    // Insert all guest events for the user
    const eventsToInsert = guestEvents.map(event => ({
      user_id: userId,
      event_name: event.event_name,
      created_at: event.created_at,
    }));

    const { error } = await supabase
      .from('user_events')
      .insert(eventsToInsert);

    if (error) {
      logger.error('Error migrating guest events:', error);
      return false;
    }

    // Clear guest events from local storage
    await safeStorageRemoveItem('guest_events');

    logger.info('Successfully migrated guest events to user:', { 
      userId, 
      eventCount: guestEvents.length 
    });

    return true;
  } catch (error) {
    logger.error('Error migrating guest events:', error);
    return false;
  }
}

export async function clearGuestEvents(): Promise<void> {
  try {
    await safeStorageRemoveItem('guest_events');
    logger.info('Guest events cleared');
  } catch (error) {
    logger.error('Error clearing guest events:', error);
  }
}
