/**
 * Secure logging utility to replace console.log statements
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  userId?: string;
  sessionId?: string;
}

class SecureLogger {
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;
  private isProduction: boolean;

  constructor() {
    // Set log level based on environment
    this.isProduction = process.env.NODE_ENV === 'production';
    this.logLevel = this.isProduction ? LogLevel.ERROR : LogLevel.DEBUG;
  }

  /**
   * Set the minimum log level
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Log debug information (only in development)
   */
  debug(message: string, data?: any): void {
    if (this.logLevel <= LogLevel.DEBUG && !this.isProduction) {
      this.log(LogLevel.DEBUG, message, data);
    }
  }

  /**
   * Log general information
   */
  info(message: string, data?: any): void {
    if (this.logLevel <= LogLevel.INFO) {
      this.log(LogLevel.INFO, message, data);
    }
  }

  /**
   * Log warnings
   */
  warn(message: string, data?: any): void {
    if (this.logLevel <= LogLevel.WARN) {
      this.log(LogLevel.WARN, message, data);
    }
  }

  /**
   * Log errors
   */
  error(message: string, error?: any): void {
    if (this.logLevel <= LogLevel.ERROR) {
      this.log(LogLevel.ERROR, message, error);
    }
  }

  /**
   * Internal logging method
   */
  private log(level: LogLevel, message: string, data?: any): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message: this.sanitizeMessage(message),
      data: this.sanitizeData(data),
      userId: this.getCurrentUserId(),
      sessionId: this.getCurrentSessionId()
    };

    this.logs.push(entry);

    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // In production, only show errors in console
    if (this.isProduction && level >= LogLevel.ERROR) {
      console.error(`[${entry.timestamp}] ${entry.message}`, entry.data);
    } else if (!this.isProduction) {
      // In development, show all logs
      const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
      console.log(`[${levelNames[level]}] ${entry.message}`, entry.data || '');
    }
  }

  /**
   * Sanitize log messages to prevent log injection
   */
  private sanitizeMessage(message: string): string {
    if (typeof message !== 'string') {
      return String(message);
    }

    // Remove newlines and excessive whitespace
    return message
      .replace(/\n/g, ' ')
      .replace(/\r/g, ' ')
      .replace(/\t/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
      .substring(0, 1000); // Limit message length
  }

  /**
   * Sanitize log data to prevent sensitive information leakage
   */
  private sanitizeData(data: any): any {
    if (!data) return data;

    try {
      // Create a copy to avoid modifying original data
      const sanitized = JSON.parse(JSON.stringify(data));

      // Remove sensitive fields
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credential'];
      
      const removeSensitiveData = (obj: any): any => {
        if (typeof obj === 'object' && obj !== null) {
          for (const key in obj) {
            if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
              obj[key] = '[REDACTED]';
            } else if (typeof obj[key] === 'object') {
              removeSensitiveData(obj[key]);
            }
          }
        }
        return obj;
      };

      return removeSensitiveData(sanitized);
    } catch {
      return '[Data sanitization failed]';
    }
  }

  /**
   * Get current user ID (to be implemented with auth system)
   */
  private getCurrentUserId(): string | undefined {
    // TODO: Implement when auth system is added
    return undefined;
  }

  /**
   * Get current session ID (to be implemented with auth system)
   */
  private getCurrentSessionId(): string | undefined {
    // TODO: Implement when auth system is added
    return undefined;
  }

  /**
   * Get all logs (for debugging purposes)
   */
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
  }

  /**
   * Export logs for analysis
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Export singleton instance
export const logger = new SecureLogger();

// Convenience functions
export const debug = (message: string, data?: any) => logger.debug(message, data);
export const info = (message: string, data?: any) => logger.info(message, data);
export const warn = (message: string, data?: any) => logger.warn(message, data);
export const error = (message: string, error?: any) => logger.error(message, error);
