import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';
import { logger } from './logger';

export class SecureStorage {
  private static instance: SecureStorage;
  private encryptionKey: string | null = null;

  private constructor() {}

  public static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage();
    }
    return SecureStorage.instance;
  }

  /**
   * Generate or retrieve encryption key for web platform
   */
  private async getEncryptionKey(): Promise<string> {
    if (this.encryptionKey) {
      return this.encryptionKey;
    }

    if (Platform.OS === 'web') {
      // Try to get existing key from localStorage
      const existingKey = window.localStorage?.getItem('__secure_storage_key__');
      if (existingKey) {
        this.encryptionKey = existingKey;
        return existingKey;
      }

      // Generate new key and store it
      const newKey = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        `${Date.now()}-${Math.random()}-${navigator.userAgent || 'web'}`
      );

      window.localStorage?.setItem('__secure_storage_key__', newKey);
      this.encryptionKey = newKey;
      return newKey;
    }

    // For native platforms, we don't need a separate key as SecureStore handles encryption
    return '';
  }

  /**
   * Encrypt data for web storage
   */
  private async encryptData(data: string): Promise<string> {
    if (Platform.OS !== 'web') {
      return data; // Native platforms use SecureStore encryption
    }

    try {
      const key = await this.getEncryptionKey();
      const encrypted = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        key + data
      );

      // Simple XOR-like encryption with the key
      const keyBytes = new TextEncoder().encode(key);
      const dataBytes = new TextEncoder().encode(data);
      const encryptedBytes = new Uint8Array(dataBytes.length);

      for (let i = 0; i < dataBytes.length; i++) {
        encryptedBytes[i] = dataBytes[i] ^ keyBytes[i % keyBytes.length];
      }

      // Convert to base64 for storage
      return btoa(String.fromCharCode(...encryptedBytes));
    } catch (error) {
      logger.error('Error encrypting data:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt data from web storage
   */
  private async decryptData(encryptedData: string): Promise<string> {
    if (Platform.OS !== 'web') {
      return encryptedData; // Native platforms use SecureStore decryption
    }

    try {
      const key = await this.getEncryptionKey();

      // Convert from base64
      const encryptedBytes = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      // Decrypt using XOR with key
      const keyBytes = new TextEncoder().encode(key);
      const decryptedBytes = new Uint8Array(encryptedBytes.length);

      for (let i = 0; i < encryptedBytes.length; i++) {
        decryptedBytes[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
      }

      return new TextDecoder().decode(decryptedBytes);
    } catch (error) {
      logger.error('Error decrypting data:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Store data securely
   */
  async setItem(key: string, value: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);

      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          // Encrypt data before storing on web
          const encryptedValue = await this.encryptData(jsonValue);
          window.localStorage.setItem(key, encryptedValue);
          logger.debug('Data stored securely on web with encryption');
        } else {
          throw new Error('localStorage not available on web platform');
        }
      } else {
        // Use SecureStore for native platforms (already encrypted)
        await SecureStore.setItemAsync(key, jsonValue);
        logger.debug('Data stored securely using SecureStore');
      }
    } catch (error) {
      logger.error('SecureStorage: Error setting item:', error);
      throw new Error('Failed to store data securely');
    }
  }

  /**
   * Retrieve data securely
   */
  async getItem<T>(key: string): Promise<T | null> {
    try {
      let value: string | null = null;

      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          const encryptedValue = window.localStorage.getItem(key);
          if (encryptedValue) {
            // Decrypt data retrieved from web storage
            value = await this.decryptData(encryptedValue);
            logger.debug('Data retrieved and decrypted from web storage');
          }
        } else {
          return null;
        }
      } else {
        // Use SecureStore for native platforms (automatically decrypted)
        value = await SecureStore.getItemAsync(key);
        if (value) {
          logger.debug('Data retrieved from SecureStore');
        }
      }

      if (value === null) {
        return null;
      }
      return JSON.parse(value);
    } catch (error) {
      logger.error('SecureStorage: Error getting item:', error);
      return null;
    }
  }

  /**
   * Remove data securely
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.removeItem(key);
          logger.debug('Data removed from web storage');
        }
      } else {
        await SecureStore.deleteItemAsync(key);
        logger.debug('Data removed from SecureStore');
      }
    } catch (error) {
      logger.error('SecureStorage: Error removing item:', error);
      throw new Error('Failed to remove data securely');
    }
  }

  /**
   * Clear all stored data and encryption keys (for logout/reset)
   */
  async clearAll(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        if (typeof window !== 'undefined' && window.localStorage) {
          // Clear all app data but preserve other localStorage data
          const keysToRemove: string[] = [];
          for (let i = 0; i < window.localStorage.length; i++) {
            const key = window.localStorage.key(i);
            if (key && (key.startsWith('everlasting_') || key === '__secure_storage_key__')) {
              keysToRemove.push(key);
            }
          }
          keysToRemove.forEach(key => window.localStorage.removeItem(key));
          this.encryptionKey = null;
          logger.info('All secure data cleared from web storage');
        }
      } else {
        // For native platforms, we'd need to track keys or clear specific known keys
        logger.info('SecureStore data clearing not implemented for native platforms');
      }
    } catch (error) {
      logger.error('SecureStorage: Error clearing all data:', error);
      throw new Error('Failed to clear all data securely');
    }
  }

  /**
   * Check if secure storage is available and working
   */
  async isAvailable(): Promise<boolean> {
    try {
      if (Platform.OS === 'web') {
        // Check if localStorage is available and crypto functions work
        if (typeof window === 'undefined' || !window.localStorage) {
          return false;
        }

        // Test encryption/decryption functionality
        const testData = 'test_data_' + Date.now();
        const encrypted = await this.encryptData(testData);
        const decrypted = await this.decryptData(encrypted);

        return decrypted === testData;
      } else {
        // Test SecureStore functionality
        const testKey = '__secure_storage_test__';
        await SecureStore.setItemAsync(testKey, 'test');
        const retrieved = await SecureStore.getItemAsync(testKey);
        await SecureStore.deleteItemAsync(testKey);

        return retrieved === 'test';
      }
    } catch (error) {
      logger.error('SecureStorage availability test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const secureStorage = SecureStorage.getInstance();
