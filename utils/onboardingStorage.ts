import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import { logger } from './logger';
import { hasGuestEvent } from '../hooks/useUserEvents';

const ONBOARDING_COMPLETED_KEY = 'onboarding_completed';

export async function hasCompletedOnboarding(): Promise<boolean> {
  try {
    // Simplified version - just check local storage for now
    const value = await AsyncStorage.getItem(ONBOARDING_COMPLETED_KEY);
    return value === 'true';
  } catch (error) {
    logger.error('Error checking onboarding status:', error);
    return false;
  }
}

export async function markOnboardingCompleted(): Promise<void> {
  try {
    // Always mark in local storage as backup
    await AsyncStorage.setItem(ONBOARDING_COMPLETED_KEY, 'true');
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      // For authenticated users, also log to Supabase
      const { error } = await supabase
        .from('user_events')
        .insert({
          user_id: user.id,
          event_name: 'onboarding_completed',
        });

      if (error) {
        logger.error('Error logging onboarding completion to Supabase:', error);
        // Don't throw error, local storage is already updated
      } else {
        logger.info('Onboarding completion logged to Supabase for user:', user.id);
      }
    }
  } catch (error) {
    logger.error('Error marking onboarding as completed:', error);
  }
}

export async function resetOnboarding(): Promise<void> {
  try {
    // Clear local storage
    await AsyncStorage.removeItem(ONBOARDING_COMPLETED_KEY);
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      // For authenticated users, also remove from Supabase
      const { error } = await supabase
        .from('user_events')
        .delete()
        .eq('user_id', user.id)
        .eq('event_name', 'onboarding_completed');

      if (error) {
        logger.error('Error removing onboarding completion from Supabase:', error);
      } else {
        logger.info('Onboarding completion removed from Supabase for user:', user.id);
      }
    }
  } catch (error) {
    logger.error('Error resetting onboarding:', error);
  }
}
