/**
 * Date utility functions for proper timezone handling
 * Uses the user's local device timezone
 */

/**
 * Normalizes a timestamp to the start of the day in the user's local timezone
 * This ensures consistent day calculations regardless of when during the day the activity occurs
 */
export const normalizeToStartOfDay = (timestamp: number): number => {
  const date = new Date(timestamp);
  // Set to start of day in user's local timezone
  date.setHours(0, 0, 0, 0);
  return date.getTime();
};

/**
 * Gets the start of today in the user's local timezone
 */
export const getStartOfToday = (): number => {
  return normalizeToStartOfDay(Date.now());
};

/**
 * Calculates the difference in days between two timestamps
 * Uses normalized dates to ensure accurate day counting
 */
export const getDaysDifference = (timestamp1: number, timestamp2: number): number => {
  const day1 = normalizeToStartOfDay(timestamp1);
  const day2 = normalizeToStartOfDay(timestamp2);
  
  return Math.floor((day2 - day1) / (1000 * 60 * 60 * 24));
};

/**
 * Checks if two timestamps are on the same day in the user's local timezone
 */
export const isSameDay = (timestamp1: number, timestamp2: number): boolean => {
  return getDaysDifference(timestamp1, timestamp2) === 0;
};

/**
 * Checks if two timestamps are on consecutive days in the user's local timezone
 */
export const isConsecutiveDay = (timestamp1: number, timestamp2: number): boolean => {
  return getDaysDifference(timestamp1, timestamp2) === 1;
};

/**
 * Gets a human-readable date string in the user's local timezone
 */
export const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};

/**
 * Gets a human-readable time string in the user's local timezone
 */
export const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString();
};
