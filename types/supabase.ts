// Generated types for Supabase database schema

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      date_night_ideas_global: {
        Row: {
          id: string
          slug: string | null
          title: string
          description: string
          source: IdeaSource
          week_number: number | null
          emoji: string | null
          category: string | null
          difficulty: DifficultyLevel
          estimated_duration: number | null
          cost: CostLevel
          indoor_outdoor: LocationType
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          slug?: string | null
          title: string
          description: string
          source?: IdeaSource
          week_number?: number | null
          emoji?: string | null
          category?: string | null
          difficulty: DifficultyLevel
          estimated_duration?: number | null
          cost: CostLevel
          indoor_outdoor: LocationType
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          slug?: string | null
          title?: string
          description?: string
          source?: IdeaSource
          week_number?: number | null
          emoji?: string | null
          category?: string | null
          difficulty?: DifficultyLevel
          estimated_duration?: number | null
          cost?: CostLevel
          indoor_outdoor?: LocationType
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      date_night_ideas_user: {
        Row: {
          id: string
          user_id: string | null
          title: string
          description: string | null
          emoji: string | null
          category: string | null
          difficulty: DifficultyLevel | null
          estimated_duration: number | null
          cost: CostLevel | null
          indoor_outdoor: LocationType | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id?: string | null
          title: string
          description?: string | null
          emoji?: string | null
          category?: string | null
          difficulty?: DifficultyLevel | null
          estimated_duration?: number | null
          cost?: CostLevel | null
          indoor_outdoor?: LocationType | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string | null
          title?: string
          description?: string | null
          emoji?: string | null
          category?: string | null
          difficulty?: DifficultyLevel | null
          estimated_duration?: number | null
          cost?: CostLevel | null
          indoor_outdoor?: LocationType | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "date_night_ideas_user_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      idea_source: "system" | "weekly" | "user"
      difficulty_level: "easy" | "medium" | "hard"
      cost_level: "free" | "low" | "medium" | "high"
      location_type: "indoor" | "outdoor" | "both"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Type aliases for better readability
export type IdeaSource = Database['public']['Enums']['idea_source']
export type DifficultyLevel = Database['public']['Enums']['difficulty_level']
export type CostLevel = Database['public']['Enums']['cost_level']
export type LocationType = Database['public']['Enums']['location_type']

// Table row types
export type DateNightIdeaGlobal = Database['public']['Tables']['date_night_ideas_global']['Row']
export type DateNightIdeaUser = Database['public']['Tables']['date_night_ideas_user']['Row']

// Insert types
export type DateNightIdeaGlobalInsert = Database['public']['Tables']['date_night_ideas_global']['Insert']
export type DateNightIdeaUserInsert = Database['public']['Tables']['date_night_ideas_user']['Insert']

// Update types
export type DateNightIdeaGlobalUpdate = Database['public']['Tables']['date_night_ideas_global']['Update']
export type DateNightIdeaUserUpdate = Database['public']['Tables']['date_night_ideas_user']['Update']

// Combined types for UI
export type DateNightIdea = DateNightIdeaGlobal & {
  isUserIdea?: boolean
  userStatus?: 'favorite' | 'planned' | 'completed'
}

// Filter types
export interface DateNightFilters {
  category?: string
  cost?: CostLevel
  indoor_outdoor?: LocationType
  difficulty?: DifficultyLevel
  week_number?: number
  search?: string
}

// API response types
export interface PaginatedResponse<T> {
  data: T[]
  count: number
  hasMore: boolean
}

export interface ApiError {
  message: string
  code?: string
  details?: any
}
