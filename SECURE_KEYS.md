# 🔐 Secure Keys Storage

## ⚠️ IMPORTANT SECURITY NOTICE

**NEVER** put the `service_role` key in your `.env` file or client-side code!

## Your Supabase Keys:

### ✅ Client-Side (Safe to use in .env):
- **Project URL**: `https://gvnizqgiqiotrysrvdkb.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd2bml6cWdpcWlvdHJ5c3J2ZGtiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY5OTUzNzksImV4cCI6MjA3MjU3MTM3OX0.g-CToK0RCrB94XjIz9IvPLEnJQMDuX92QPtmTKrx5Us`

### 🔒 Server-Side Only (NEVER in client code):
- **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd2bml6cWdpcWlvdHJ5c3J2ZGtiIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Njk5NTM3OSwiZXhwIjoyMDcyNTcxMzc5fQ.o6ne2dXOCdTb4PuPrvHh2Gyx7L88Fx52fKvlHEoS1v8`

## When to Use Each Key:

### Anon Key (Client-Side):
- ✅ User authentication
- ✅ Reading public data
- ✅ User-specific data (with RLS policies)
- ✅ Safe to include in your app

### Service Role Key (Server-Side Only):
- ❌ NEVER in client-side code
- ✅ Server-side operations only
- ✅ Admin operations
- ✅ Bypasses Row Level Security
- ✅ Store in secure server environment variables

## Security Best Practices:

1. **Anon key** is designed for public use - it's safe in your app
2. **Service role key** bypasses all security - keep it secret!
3. **Row Level Security (RLS)** policies protect your data
4. **Environment variables** keep keys out of your code
5. **Never commit** service role keys to git

## Your Current Setup:
- ✅ Anon key is in `.env` (correct)
- ✅ Service role key is documented here (secure)
- ✅ `.env` is in `.gitignore` (safe)
- ✅ App should now work without Supabase errors
